# Environment Configuration

This project uses environment variables for configuration management. All configuration is centralized in the `.env` file.

## Setup

1. The `.env` file is already provided with default configuration. Edit it if needed:

   ```bash
   nano .env
   ```

## Environment Variables

### Network Configuration

- `HOST=***************` - Your server IP address (changed from SERVER_IP)
- `FRONTEND_PORT=3000` - Frontend development server port
- `BACKEND_PORT=8080` - Backend API server port

### Application Configuration

- `SERVER_HOST=0.0.0.0` - Server bind address
- `GIN_MODE=debug` - Gin framework mode (debug/release)
- `VITE_API_BASE_URL=http://***************:8080` - Frontend API endpoint

### Database Configuration

- `DB_HOST=localhost` - Database host
- `DB_PORT=5432` - Database port
- `DB_USER=postgres` - Database username
- `DB_PASSWORD=postgres` - Database password
- `DB_NAME=release_management` - Database name

### Environment Settings

- `NODE_ENV=development` - Node.js environment mode

## Usage

### Local Development

```bash
# Start all services with environment variables
./start-services.sh
```

The script will:

- Load configuration from `.env`
- Display current settings
- Start backend with database configuration
- Start frontend with API configuration
- Monitor service health
- Handle graceful shutdown

### Docker Deployment

```bash
# Docker Compose automatically reads .env file
docker-compose up --build
```

### Manual Start

```bash
# Load environment variables
source .env

# Start backend
cd backend
PORT=$BACKEND_PORT GIN_MODE=$GIN_MODE go run main.go

# Start frontend (in another terminal)
cd frontend
VITE_API_BASE_URL=$VITE_API_BASE_URL npm run dev -- --host 0.0.0.0 --port $FRONTEND_PORT
```

## Network Access

To allow team access, set `HOST` to your machine's network IP address:

1. Find your IP:

   ```bash
   ip addr show | grep 'inet ' | grep -v '127.0.0.1'
   ```

2. Update `.env`:

   ```
   HOST=192.168.x.x  # Your actual IP
   VITE_API_BASE_URL=http://192.168.x.x:8080
   ```

3. Restart services:

   ```bash
   ./start-services.sh
   ```

## Features

### Enhanced Startup Script

The `start-services.sh` script provides:

- ✅ Colorized output for better readability
- ✅ Environment variable validation
- ✅ Process health checks
- ✅ Automatic cleanup on exit
- ✅ Detailed logging and error reporting
- ✅ Graceful shutdown handling (Ctrl+C)

### Configuration Validation

The script automatically validates:

- Environment file loading
- Service startup success
- Process health monitoring

## Troubleshooting

### Connection Issues

- Ensure `HOST` matches your actual network IP
- Check firewall settings for ports 3000 and 8080
- Verify backend is accessible: `curl http://$HOST:$BACKEND_PORT/api/releases`

### Service Startup Issues

- Check log files: `backend.log` and `frontend.log`
- Verify database connection settings
- Ensure no port conflicts

### Docker Issues

- Environment variables not loading: Check `.env` file exists in project root
- Port conflicts: Change `FRONTEND_PORT` and `BACKEND_PORT` in `.env`

### Frontend API Errors

- Check `VITE_API_BASE_URL` matches backend address
- Verify backend CORS settings allow your frontend domain
- Ensure backend service is running and healthy

## Example .env File

```env
# Release Management System Configuration

# Network Configuration
HOST=***************
FRONTEND_PORT=3000
BACKEND_PORT=8080

# Database Configuration
DB_HOST=localhost
DB_PORT=5432
DB_USER=postgres
DB_PASSWORD=postgres
DB_NAME=release_management

# Application Configuration
SERVER_HOST=0.0.0.0
GIN_MODE=debug
VITE_API_BASE_URL=http://***************:8080

# Environment
NODE_ENV=development
```
