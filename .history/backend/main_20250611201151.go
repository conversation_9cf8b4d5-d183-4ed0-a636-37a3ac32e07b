package main

import (
	"fmt"
	"log"
	"os"

	"release-management-backend/controllers"
	"release-management-backend/models"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func initDatabase() *gorm.DB {
	host := getEnv("DB_HOST", "localhost")
	port := getEnv("DB_PORT", "5432")
	user := getEnv("DB_USER", "postgres")
	password := getEnv("DB_PASSWORD", "postgres")
	dbname := getEnv("DB_NAME", "release_management")

	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=UTC",
		host, user, password, dbname, port)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Auto-migrate the schema
	err = db.AutoMigrate(
		&models.Release{},
		&models.FeatureSummary{},
		&models.CCBSummary{},
		&models.RiskPart{},
		&models.Quality{},
	)
	if err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	return db
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func setupRoutes(db *gorm.DB) *gin.Engine {
	r := gin.Default()

	// CORS middleware
	config := cors.DefaultConfig()

	// Get HOST and FRONTEND_PORT from environment
	host := getEnv("HOST", "***************")
	frontendPort := getEnv("FRONTEND_PORT", "3000")

	config.AllowOrigins = []string{
		"http://localhost:" + frontendPort,
		"http://" + host + ":" + frontendPort,
		"http://127.0.0.1:" + frontendPort,
	}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Content-Length", "Accept-Encoding", "X-CSRF-Token", "Authorization"}
	config.AllowCredentials = true
	r.Use(cors.New(config))

	// Initialize controllers
	releaseController := controllers.NewReleaseController(db)
	featureController := controllers.NewFeatureController(db)
	ccbController := controllers.NewCCBController(db)
	riskController := controllers.NewRiskController(db)
	qualityController := controllers.NewQualityController(db)

	// API routes
	api := r.Group("/api")
	{
		// Release routes
		releases := api.Group("/releases")
		{
			releases.GET("", releaseController.GetReleases)
			releases.POST("", releaseController.CreateRelease)
			releases.GET("/:id", releaseController.GetRelease)
			releases.PUT("/:id", releaseController.UpdateRelease)
			releases.DELETE("/:id", releaseController.DeleteRelease)

			// Feature routes under specific release
			releases.GET("/:id/features", featureController.GetFeatures)
			releases.POST("/:id/features", featureController.CreateFeature)

			// CCB routes under specific release
			releases.GET("/:id/ccbs", ccbController.GetCCBs)
			releases.POST("/:id/ccbs", ccbController.CreateCCB)

			// Risk routes under specific release
			releases.GET("/:id/risks", riskController.GetRisks)
			releases.POST("/:id/risks", riskController.CreateRisk)
		}

		// Feature routes
		features := api.Group("/features")
		{
			features.GET("/states", featureController.GetFeatureStates)
			features.PUT("/:id", featureController.UpdateFeature)
			features.DELETE("/:id", featureController.DeleteFeature)
		}

		// CCB routes
		ccbs := api.Group("/ccbs")
		{
			ccbs.PUT("/:id", ccbController.UpdateCCB)
			ccbs.DELETE("/:id", ccbController.DeleteCCB)
		}

		// Risk routes
		risks := api.Group("/risks")
		{
			risks.PUT("/:id", riskController.UpdateRisk)
			risks.DELETE("/:id", riskController.DeleteRisk)
		}
	}

	return r
}

func main() {
	// Initialize database
	db := initDatabase()

	// Setup routes
	r := setupRoutes(db)

	// Start server
	host := getEnv("SERVER_HOST", "0.0.0.0") // 0.0.0.0 allows all interfaces
	port := getEnv("PORT", "8080")
	address := fmt.Sprintf("%s:%s", host, port)

	// Get HOST for logging
	hostForLog := getEnv("HOST", "***************")

	log.Printf("Server starting on %s", address)
	log.Printf("API will be available at:")
	log.Printf("  - http://localhost:%s/api", port)
	log.Printf("  - http://%s:%s/api", hostForLog, port)
	log.Printf("  - http://127.0.0.1:%s/api", port)

	log.Fatal(r.Run(address))
}
