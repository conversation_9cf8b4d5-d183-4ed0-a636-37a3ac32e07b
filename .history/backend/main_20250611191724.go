package main

import (
	"fmt"
	"log"
	"os"

	"release-management-backend/controllers"
	"release-management-backend/models"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func initDatabase() *gorm.DB {
	host := getEnv("DB_HOST", "localhost")
	port := getEnv("DB_PORT", "5432")
	user := getEnv("DB_USER", "postgres")
	password := getEnv("DB_PASSWORD", "postgres")
	dbname := getEnv("DB_NAME", "release_management")

	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=UTC",
		host, user, password, dbname, port)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Auto-migrate the schema
	err = db.AutoMigrate(
		&models.Release{},
		&models.FeatureSummary{},
		&models.CCBSummary{},
		&models.RiskPart{},
		&models.Quality{},
		&models.QualityImage{},
	)
	if err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	return db
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func setupRoutes(db *gorm.DB) *gin.Engine {
	r := gin.Default()

	// Enable CORS
	r.Use(cors.New(cors.Config{
		AllowOrigins:     []string{"http://localhost:3000"},
		AllowMethods:     []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowHeaders:     []string{"Origin", "Content-Type", "Accept", "Authorization"},
		ExposeHeaders:    []string{"Content-Length"},
		AllowCredentials: true,
	}))

	// Create controllers
	releaseController := controllers.NewReleaseController(db)
	featureController := controllers.NewFeatureController(db)
	ccbController := controllers.NewCCBController(db)
	riskController := controllers.NewRiskController(db)
	qualityController := controllers.NewQualityController(db)

	// Serve static files for uploaded images
	r.Static("/uploads", "./uploads")

	// API routes
	api := r.Group("/api")
	{
		// Release routes
		releases := api.Group("/releases")
		{
			releases.GET("", releaseController.GetReleases)
			releases.POST("", releaseController.CreateRelease)
			releases.GET("/:id", releaseController.GetRelease)
			releases.PUT("/:id", releaseController.UpdateRelease)
			releases.DELETE("/:id", releaseController.DeleteRelease)

			// Feature routes under specific release
			releases.GET("/:id/features", featureController.GetFeatures)
			releases.POST("/:id/features", featureController.CreateFeature)

			// CCB routes under specific release
			releases.GET("/:id/ccbs", ccbController.GetCCBs)
			releases.POST("/:id/ccbs", ccbController.CreateCCB)

			// Risk routes under specific release
			releases.GET("/:id/risks", riskController.GetRisks)
			releases.POST("/:id/risks", riskController.CreateRisk)

			// Quality routes under specific release
			releases.GET("/:id/qualities", qualityController.GetQualities)
			releases.POST("/:id/qualities", qualityController.CreateQuality)
		}

		// Feature routes
		features := api.Group("/features")
		{
			features.GET("/states", featureController.GetFeatureStates)
			features.PUT("/:id", featureController.UpdateFeature)
			features.DELETE("/:id", featureController.DeleteFeature)
		}

		// CCB routes
		ccbs := api.Group("/ccbs")
		{
			ccbs.PUT("/:id", ccbController.UpdateCCB)
			ccbs.DELETE("/:id", ccbController.DeleteCCB)
		}

		// Risk routes
		risks := api.Group("/risks")
		{
			risks.PUT("/:id", riskController.UpdateRisk)
			risks.DELETE("/:id", riskController.DeleteRisk)
		}

		// Quality routes
		qualities := api.Group("/qualities")
		{
			qualities.PUT("/:id", qualityController.UpdateQuality)
			qualities.DELETE("/:id", qualityController.DeleteQuality)
			qualities.POST("/:id/images", qualityController.UploadQualityImage)
			qualities.DELETE("/images/:imageId", qualityController.DeleteQualityImage)
		}
	}

	return r
}

func main() {
	// Initialize database
	db := initDatabase()

	// Setup routes
	r := setupRoutes(db)

	// Start server
	host := getEnv("SERVER_HOST", "0.0.0.0") // 0.0.0.0 allows all interfaces
	port := getEnv("PORT", "9001")
	address := fmt.Sprintf("%s:%s", host, port)

	// Get HOST for logging
	hostForLog := getEnv("HOST", "***************")

	log.Printf("Server starting on %s", address)
	log.Printf("API will be available at:")
	log.Printf("  - http://localhost:%s/api", port)
	log.Printf("  - http://%s:%s/api", hostForLog, port)
	log.Printf("  - http://127.0.0.1:%s/api", port)

	log.Fatal(r.Run(address))
}
