package controllers

import (
	"fmt"
	"net/http"
	"strconv"

	"release-management-backend/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type ReleaseController struct {
	DB *gorm.DB
}

func NewReleaseController(db *gorm.DB) *ReleaseController {
	return &ReleaseController{DB: db}
}

// GetReleases gets all releases
func (rc *ReleaseController) GetReleases(c *gin.Context) {
	var releases []models.Release

	result := rc.DB.Preload("FeatureSummaries").Preload("CCBSummaries").Preload("RiskParts").Preload("Qualities").Find(&releases)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, releases)
}

// GetRelease gets a single release by ID
func (rc *ReleaseController) GetRelease(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.<PERSON>(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var release models.Release
	result := rc.DB.Preload("FeatureSummaries").Preload("CCBSummaries").Preload("RiskParts").Preload("Qualities").First(&release, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Release not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, release)
}

// CreateRelease creates a new release
func (rc *ReleaseController) CreateRelease(c *gin.Context) {
	var release models.Release

	if err := c.ShouldBindJSON(&release); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	result := rc.DB.Create(&release)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusCreated, release)
}

// UpdateRelease updates an existing release
func (rc *ReleaseController) UpdateRelease(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var release models.Release
	if result := rc.DB.First(&release, id); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Release not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Store original release for comparison
	originalRelease := release

	if err := c.ShouldBindJSON(&release); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Debug: Print the received data
	fmt.Printf("Updating release ID %d\n", id)
	fmt.Printf("Original RiskLink: '%s', New RiskLink: '%s'\n", originalRelease.RiskLink, release.RiskLink)
	fmt.Printf("Original SoftwareDownload: '%s', New SoftwareDownload: '%s'\n", originalRelease.SoftwareDownload, release.SoftwareDownload)
	fmt.Printf("Original DocLink: '%s', New DocLink: '%s'\n", originalRelease.DocLink, release.DocLink)

	if result := rc.DB.Save(&release); result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, release)
}

// DeleteRelease deletes a release
func (rc *ReleaseController) DeleteRelease(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	result := rc.DB.Delete(&models.Release{}, id)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Release not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Release deleted successfully"})
}
