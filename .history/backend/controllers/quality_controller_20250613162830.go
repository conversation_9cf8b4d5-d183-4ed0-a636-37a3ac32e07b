package controllers

import (
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"release-management-backend/models"
	"strconv"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type QualityController struct {
	DB *gorm.DB
}

func NewQualityController(db *gorm.DB) *QualityController {
	return &QualityController{DB: db}
}

// GetQualities - Get all qualities for a release
func (qc *QualityController) GetQualities(c *gin.Context) {
	releaseID := c.Param("id")
	var qualities []models.Quality

	result := qc.DB.Where("release_id = ?", releaseID).Find(&qualities)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, qualities)
}

// CreateQuality - Create a new quality image
func (qc *QualityController) CreateQuality(c *gin.Context) {
	releaseID := c.Param("id")

	// Convert releaseID to uint
	id, err := strconv.ParseUint(releaseID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	// Check if release exists
	var release models.Release
	if result := qc.DB.First(&release, id); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Release not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Parse multipart form
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get image file"})
		return
	}
	defer file.Close()

	title := c.PostForm("title")
	if title == "" {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Title is required"})
		return
	}

	// Validate file type
	if !isValidImageType(header.Filename) {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid image format. Only JPG, PNG, GIF are allowed"})
		return
	}

	// Create uploads directory if it doesn't exist
	uploadsDir := "uploads/quality"
	if err := os.MkdirAll(uploadsDir, 0755); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create upload directory"})
		return
	}

	// Generate unique filename
	ext := filepath.Ext(header.Filename)
	filename := fmt.Sprintf("quality_%d_%d%s", id, time.Now().Unix(), ext)
	filePath := filepath.Join(uploadsDir, filename)

	// Save file
	dst, err := os.Create(filePath)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create file"})
		return
	}
	defer dst.Close()

	if _, err := io.Copy(dst, file); err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file"})
		return
	}

	// Create quality record
	// Get backend base URL for complete image URL
	host := os.Getenv("HOST")
	if host == "" {
		host = "***************"
	}
	port := os.Getenv("PORT")
	if port == "" {
		port = "8888"
	}
	baseURL := fmt.Sprintf("http://%s:%s", host, port)

	quality := models.Quality{
		ReleaseID: uint(id),
		Title:     title,
		ImageURL:  fmt.Sprintf("%s/uploads/quality/%s", baseURL, filename),
	}

	result := qc.DB.Create(&quality)
	if result.Error != nil {
		// Clean up uploaded file if database insert fails
		os.Remove(filePath)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusCreated, quality)
}

// UpdateQuality - Update a quality image
func (qc *QualityController) UpdateQuality(c *gin.Context) {
	id := c.Param("id")
	var quality models.Quality

	// Find the quality
	result := qc.DB.First(&quality, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Quality not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Check if this is a multipart form (image upload) or JSON (title only)
	contentType := c.GetHeader("Content-Type")
	if strings.Contains(contentType, "multipart/form-data") {
		// Handle file upload
		file, header, err := c.Request.FormFile("image")
		if err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get image file"})
			return
		}
		defer file.Close()

		title := c.PostForm("title")
		if title == "" {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Title is required"})
			return
		}

		// Validate file type
		if !isValidImageType(header.Filename) {
			c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid image format. Only JPG, PNG, GIF are allowed"})
			return
		}

		// Create uploads directory if it doesn't exist
		uploadsDir := "uploads/quality"
		if err := os.MkdirAll(uploadsDir, 0755); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create upload directory"})
			return
		}

		// Generate unique filename
		ext := filepath.Ext(header.Filename)
		filename := fmt.Sprintf("quality_%d_%d%s", quality.ReleaseID, time.Now().Unix(), ext)
		newFilepath := filepath.Join(uploadsDir, filename)

		// Save new file
		dst, err := os.Create(newFilepath)
		if err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to create file"})
			return
		}
		defer dst.Close()

		if _, err := io.Copy(dst, file); err != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save file"})
			return
		}

		// Delete old file if it exists
		if quality.ImageURL != "" {
			idx := strings.Index(quality.ImageURL, "/uploads/quality/")
			if idx != -1 {
				relativePath := quality.ImageURL[idx+1:] // 去掉最前面的斜杠
				_ = os.Remove(relativePath)
			}
		}

		// Update quality record
		host := os.Getenv("HOST")
		if host == "" {
			host = "***************"
		}
		port := os.Getenv("PORT")
		if port == "" {
			port = "8888"
		}
		baseURL := fmt.Sprintf("http://%s:%s", host, port)

		quality.Title = title
		quality.ImageURL = fmt.Sprintf("%s/uploads/quality/%s", baseURL, filename)

		result = qc.DB.Save(&quality)
		if result.Error != nil {
			// Clean up new file if database update fails
			os.Remove(newFilepath)
			c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
			return
		}
	} else {
		// Handle JSON update (title only)
		var updateData struct {
			Title string `json:"title"`
		}
		if err := c.ShouldBindJSON(&updateData); err != nil {
			c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
			return
		}

		quality.Title = updateData.Title
		result = qc.DB.Save(&quality)
		if result.Error != nil {
			c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
			return
		}
	}

	c.JSON(http.StatusOK, quality)
}

// DeleteQuality - Delete a quality image
func (qc *QualityController) DeleteQuality(c *gin.Context) {
	id := c.Param("id")
	var quality models.Quality

	result := qc.DB.First(&quality, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Quality not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// Delete the file if it exists
	if quality.ImageURL != "" {
		idx := strings.Index(quality.ImageURL, "/uploads/quality/")
		if idx != -1 {
			relativePath := quality.ImageURL[idx+1:] // 去掉最前面的斜杠
			_ = os.Remove(relativePath)
		}
	}

	result = qc.DB.Delete(&quality)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Quality deleted successfully"})
}

// Helper function to validate image file types
func isValidImageType(filename string) bool {
	ext := strings.ToLower(filepath.Ext(filename))
	return ext == ".jpg" || ext == ".jpeg" || ext == ".png" || ext == ".gif"
}
