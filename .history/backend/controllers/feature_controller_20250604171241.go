package controllers

import (
	"net/http"
	"strconv"

	"release-management-backend/models"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

type FeatureController struct {
	DB *gorm.DB
}

func NewFeatureController(db *gorm.DB) *FeatureController {
	return &FeatureController{DB: db}
}

// GetFeatures gets all features for a release
func (fc *FeatureController) GetFeatures(c *gin.Context) {
	releaseID, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	var features []models.FeatureSummary
	result := fc.DB.Where("release_id = ?", releaseID).Find(&features)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, features)
}

// CreateFeature creates a new feature
func (fc *FeatureController) CreateFeature(c *gin.Context) {
	releaseID, err := strconv.Atoi(c.<PERSON>m("releaseId"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}

	var feature models.FeatureSummary
	if err := c.ShouldBindJSON(&feature); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	feature.ReleaseID = uint(releaseID)
	result := fc.DB.Create(&feature)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusCreated, feature)
}

// UpdateFeature updates an existing feature
func (fc *FeatureController) UpdateFeature(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	var feature models.FeatureSummary
	if result := fc.DB.First(&feature, id); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Feature not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	if err := c.ShouldBindJSON(&feature); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if result := fc.DB.Save(&feature); result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, feature)
}

// DeleteFeature deletes a feature
func (fc *FeatureController) DeleteFeature(c *gin.Context) {
	id, err := strconv.Atoi(c.Param("id"))
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid ID"})
		return
	}

	result := fc.DB.Delete(&models.FeatureSummary{}, id)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	if result.RowsAffected == 0 {
		c.JSON(http.StatusNotFound, gin.H{"error": "Feature not found"})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Feature deleted successfully"})
}
