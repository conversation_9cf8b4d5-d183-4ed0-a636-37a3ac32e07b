package controllers

import (
	"fmt"
	"image"
	_ "image/gif"
	_ "image/jpeg"
	_ "image/png"
	"io"
	"mime/multipart"
	"net/http"
	"os"
	"path/filepath"
	"release-management-backend/models"
	"strconv"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
	"gorm.io/gorm"
)

type QualityController struct {
	DB *gorm.DB
}

func NewQualityController(db *gorm.DB) *QualityController {
	return &QualityController{DB: db}
}

// GetQualities - 获取某个release的所有质量记录
func (qc *QualityController) GetQualities(c *gin.Context) {
	releaseID := c.Param("id")
	var qualities []models.Quality

	result := qc.DB.Preload("Images").Where("release_id = ?", releaseID).Find(&qualities)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, qualities)
}

// CreateQuality - 创建新的质量记录
func (qc *QualityController) CreateQuality(c *gin.Context) {
	releaseID := c.Param("id")
	var quality models.Quality

	if err := c.ShouldBindJSON(&quality); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 转换releaseID
	id, err := strconv.ParseUint(releaseID, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid release ID"})
		return
	}
	quality.ReleaseID = uint(id)

	result := qc.DB.Create(&quality)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusCreated, quality)
}

// UpdateQuality - 更新质量记录
func (qc *QualityController) UpdateQuality(c *gin.Context) {
	id := c.Param("id")
	var quality models.Quality

	// 查找质量记录
	result := qc.DB.First(&quality, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Quality not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// 更新质量记录
	if err := c.ShouldBindJSON(&quality); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	result = qc.DB.Save(&quality)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, quality)
}

// DeleteQuality - 删除质量记录及其所有图片
func (qc *QualityController) DeleteQuality(c *gin.Context) {
	id := c.Param("id")
	var quality models.Quality

	// 预加载图片信息
	result := qc.DB.Preload("Images").First(&quality, id)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Quality not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// 删除物理文件
	for _, img := range quality.Images {
		qc.deleteImageFile(img.Filename)
	}

	// 删除数据库记录（GORM会自动删除关联的图片记录）
	result = qc.DB.Delete(&quality)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Quality deleted successfully"})
}

// UploadQualityImage - 上传质量图片
func (qc *QualityController) UploadQualityImage(c *gin.Context) {
	qualityIDStr := c.Param("id")
	qualityID, err := strconv.ParseUint(qualityIDStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Invalid quality ID"})
		return
	}

	// 验证quality是否存在
	var quality models.Quality
	if result := qc.DB.First(&quality, qualityID); result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Quality not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// 获取上传的文件
	file, header, err := c.Request.FormFile("image")
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": "Failed to get uploaded file"})
		return
	}
	defer file.Close()

	// 验证文件
	if err := qc.validateImageFile(header); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// 保存文件
	imageInfo, err := qc.saveImageFile(file, header)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": "Failed to save image: " + err.Error()})
		return
	}

	// 创建数据库记录
	qualityImage := models.QualityImage{
		QualityID:    uint(qualityID),
		Filename:     imageInfo.Filename,
		OriginalName: header.Filename,
		FileSize:     header.Size,
		MimeType:     imageInfo.MimeType,
		Width:        imageInfo.Width,
		Height:       imageInfo.Height,
	}

	result := qc.DB.Create(&qualityImage)
	if result.Error != nil {
		// 如果数据库保存失败，删除已保存的文件
		qc.deleteImageFile(imageInfo.Filename)
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusCreated, qualityImage)
}

// DeleteQualityImage - 删除质量图片
func (qc *QualityController) DeleteQualityImage(c *gin.Context) {
	imageID := c.Param("imageId")
	var qualityImage models.QualityImage

	result := qc.DB.First(&qualityImage, imageID)
	if result.Error != nil {
		if result.Error == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, gin.H{"error": "Image not found"})
			return
		}
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	// 删除物理文件
	qc.deleteImageFile(qualityImage.Filename)

	// 删除数据库记录
	result = qc.DB.Delete(&qualityImage)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{"error": result.Error.Error()})
		return
	}

	c.JSON(http.StatusOK, gin.H{"message": "Image deleted successfully"})
}

// 图片信息结构
type ImageInfo struct {
	Filename string
	MimeType string
	Width    int
	Height   int
}

// validateImageFile - 验证图片文件
func (qc *QualityController) validateImageFile(header *multipart.FileHeader) error {
	// 检查文件大小 (5MB限制)
	const maxSize = 5 * 1024 * 1024 // 5MB
	if header.Size > maxSize {
		return fmt.Errorf("file size exceeds 5MB limit")
	}

	// 检查文件扩展名
	ext := strings.ToLower(filepath.Ext(header.Filename))
	allowedExts := map[string]bool{
		".jpg":  true,
		".jpeg": true,
		".png":  true,
		".gif":  true,
	}

	if !allowedExts[ext] {
		return fmt.Errorf("unsupported file type. Only JPG, PNG, and GIF are allowed")
	}

	return nil
}

// saveImageFile - 保存图片文件
func (qc *QualityController) saveImageFile(file multipart.File, header *multipart.FileHeader) (*ImageInfo, error) {
	// 创建uploads目录
	uploadDir := "uploads/quality-images"
	if err := os.MkdirAll(uploadDir, 0755); err != nil {
		return nil, fmt.Errorf("failed to create upload directory: %v", err)
	}

	// 生成唯一文件名
	ext := strings.ToLower(filepath.Ext(header.Filename))
	filename := uuid.New().String() + ext
	filepath := filepath.Join(uploadDir, filename)

	// 创建目标文件
	dst, err := os.Create(filepath)
	if err != nil {
		return nil, fmt.Errorf("failed to create file: %v", err)
	}
	defer dst.Close()

	// 重置file指针
	file.Seek(0, 0)

	// 复制文件内容
	if _, err := io.Copy(dst, file); err != nil {
		return nil, fmt.Errorf("failed to save file: %v", err)
	}

	// 获取图片尺寸
	file.Seek(0, 0)
	config, _, err := image.DecodeConfig(file)
	width, height := 0, 0
	if err == nil {
		width = config.Width
		height = config.Height
	}

	return &ImageInfo{
		Filename: filename,
		MimeType: header.Header.Get("Content-Type"),
		Width:    width,
		Height:   height,
	}, nil
}

// deleteImageFile - 删除图片文件
func (qc *QualityController) deleteImageFile(filename string) {
	filepath := filepath.Join("uploads/quality-images", filename)
	os.Remove(filepath) // 忽略错误，文件可能已经不存在
}
