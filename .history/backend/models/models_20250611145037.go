package models

import (
	"database/sql/driver"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// CustomDate handles date parsing for forms
type CustomDate struct {
	time.Time
}

func (cd *CustomDate) UnmarshalJSON(b []byte) error {
	s := strings.Trim(string(b), "\"")
	if s == "null" || s == "" {
		cd.Time = time.Time{}
		return nil
	}

	// Try different date formats
	formats := []string{
		"2006-01-02",
		"2006-01-02T15:04:05Z07:00",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, s); err == nil {
			cd.Time = t
			return nil
		}
	}

	return fmt.Errorf("cannot parse %q as date", s)
}

func (cd CustomDate) MarshalJSON() ([]byte, error) {
	if cd.Time.IsZero() {
		return []byte("null"), nil
	}
	return []byte("\"" + cd.Time.Format("2006-01-02") + "\""), nil
}

func (cd CustomDate) Value() (driver.Value, error) {
	if cd.Time.IsZero() {
		return nil, nil
	}
	return cd.Time, nil
}

func (cd *CustomDate) Scan(value interface{}) error {
	if value == nil {
		cd.Time = time.Time{}
		return nil
	}
	cd.Time = value.(time.Time)
	return nil
}

// Release model
type Release struct {
	ID          uint   `json:"id" gorm:"primaryKey"`
	Name        string `json:"name" gorm:"not null"`
	Version     string `json:"version" gorm:"index"`         // 版本号，如 v1.2.3
	Description string `json:"description" gorm:"type:text"` // 发布描述
	Type        string `json:"type"`                         // 发布类型：major, minor, patch, hotfix
	Priority    string `json:"priority"`                     // 优先级：low, medium, high, critical

	// 负责人信息
	ReleaseManager string `json:"release_manager"` // 发布经理
	ProjectManager string `json:"project_manager"` // 项目经理
	TechnicalLead  string `json:"technical_lead"`  // 技术负责人
	QALead         string `json:"qa_lead"`         // QA负责人

	// 时间规划
	PlannedStartDate    *CustomDate `json:"planned_start_date" gorm:"type:date"` // 计划开始日期
	PlannedEndDate      *CustomDate `json:"planned_end_date" gorm:"type:date"`   // 计划结束日期
	ActualStartDate     *CustomDate `json:"actual_start_date" gorm:"type:date"`  // 实际开始日期
	ActualEndDate       *CustomDate `json:"actual_end_date" gorm:"type:date"`    // 实际结束日期
	PRDSignoff          *CustomDate `json:"prd_signoff" gorm:"type:date"`
	PRDLink             string      `json:"prd_link"`
	TestStrategySignoff *CustomDate `json:"test_strategy_signoff" gorm:"type:date"`
	TestStrategyLink    string      `json:"test_strategy_link"`
	ReleaseBranchOff    *CustomDate `json:"release_branch_off" gorm:"type:date"`
	ReleaseCodeFreeze   *CustomDate `json:"release_code_freeze" gorm:"type:date"`

	// 环境部署相关
	DevDeploymentDate        *CustomDate `json:"dev_deployment_date" gorm:"type:date"`        // 开发环境部署日期
	StagingDeploymentDate    *CustomDate `json:"staging_deployment_date" gorm:"type:date"`    // 预发布环境部署日期
	ProductionDeploymentDate *CustomDate `json:"production_deployment_date" gorm:"type:date"` // 生产环境部署日期
	RollbackPlan             string      `json:"rollback_plan" gorm:"type:text"`              // 回滚计划

	// 状态和风险
	State         string `json:"state"`
	PreviousState string `json:"previous_state"`                // 上一个状态，用于状态追踪
	HealthScore   int    `json:"health_score" gorm:"default:0"` // 健康评分 0-100
	TestReport    string `json:"test_report"`
	Risk          string `json:"risk"`
	RiskState     string `json:"risk_state"`

	// 业务相关
	BusinessImpact string  `json:"business_impact" gorm:"type:text"` // 业务影响
	CustomerImpact string  `json:"customer_impact" gorm:"type:text"` // 客户影响
	RevenueImpact  float64 `json:"revenue_impact" gorm:"default:0"`  // 收入影响
	UserSegments   string  `json:"user_segments"`                    // 目标用户群体

	// 技术相关
	TechnicalChanges   string `json:"technical_changes" gorm:"type:text"`      // 技术变更说明
	DatabaseChanges    bool   `json:"database_changes" gorm:"default:false"`   // 是否包含数据库变更
	APIChanges         bool   `json:"api_changes" gorm:"default:false"`        // 是否包含API变更
	BackwardCompatible bool   `json:"backward_compatible" gorm:"default:true"` // 是否向后兼容
	SecurityChanges    bool   `json:"security_changes" gorm:"default:false"`   // 是否包含安全变更
	PerformanceImpact  string `json:"performance_impact"`                      // 性能影响评估

	// 资源和成本
	EstimatedEffort float64 `json:"estimated_effort" gorm:"default:0"` // 预估工作量（人日）
	ActualEffort    float64 `json:"actual_effort" gorm:"default:0"`    // 实际工作量（人日）
	Budget          float64 `json:"budget" gorm:"default:0"`           // 预算
	ActualCost      float64 `json:"actual_cost" gorm:"default:0"`      // 实际成本

	// 沟通和通知
	NotificationChannels string `json:"notification_channels"`               // 通知渠道
	StakeholderList      string `json:"stakeholder_list" gorm:"type:text"`   // 利益相关者列表
	CommunicationPlan    string `json:"communication_plan" gorm:"type:text"` // 沟通计划

	// 合规和审批
	ComplianceChecklist string `json:"compliance_checklist" gorm:"type:text"` // 合规检查清单
	ApprovalStatus      string `json:"approval_status"`                       // 审批状态
	SignoffRequired     bool   `json:"signoff_required" gorm:"default:false"` // 是否需要签署

	// 监控和度量
	MonitoringPlan string `json:"monitoring_plan" gorm:"type:text"` // 监控计划
	SuccessMetrics string `json:"success_metrics" gorm:"type:text"` // 成功指标
	KPIs           string `json:"kpis" gorm:"type:text"`            // 关键绩效指标

	// 原有字段
	Softwares string `json:"softwares"`
	Docs      string `json:"docs"`
	Lessons   string `json:"lessons"`

	// 系统字段
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	FeatureSummaries []FeatureSummary `json:"feature_summaries,omitempty"`
	CCBSummaries     []CCBSummary     `json:"ccb_summaries,omitempty"`
	RiskParts        []RiskPart       `json:"risk_parts,omitempty"`
	Qualities        []Quality        `json:"qualities,omitempty"`
}

// FeatureSummary model
type FeatureSummary struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	ReleaseID    uint           `json:"release_id" gorm:"not null"`
	Name         string         `json:"name"`
	Description  string         `json:"description"`
	Owner        string         `json:"owner"`
	State        string         `json:"state"`
	Started      *CustomDate    `json:"started" gorm:"type:date"`
	DevDone      *CustomDate    `json:"dev_done" gorm:"type:date"`
	Ended        *CustomDate    `json:"ended" gorm:"type:date"`
	DesignSpec   string         `json:"design_spec"`
	TestStrategy string         `json:"test_strategy"`
	TestReport   string         `json:"test_report"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// CCBSummary model
type CCBSummary struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	ReleaseID   uint           `json:"release_id" gorm:"not null"`
	Name        string         `json:"name"`
	Creator     string         `json:"creator"`
	Description string         `json:"description"`
	Created     *CustomDate    `json:"created" gorm:"type:date"`
	SignoffDate *CustomDate    `json:"signoff_date" gorm:"type:date"`
	State       string         `json:"state"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// RiskPart model
type RiskPart struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	ReleaseID        uint           `json:"release_id" gorm:"not null"`
	Team             string         `json:"team"`
	Type             string         `json:"type"`
	Description      string         `json:"description"`
	Probability      string         `json:"probability"`
	Impact           string         `json:"impact"`
	Status           string         `json:"status"`
	Migration        string         `json:"migration"`
	FallbackStrategy string         `json:"fallback_strategy"`
	Keeper           string         `json:"keeper"`
	Comments         string         `json:"comments"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// Quality model
type Quality struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	ReleaseID uint           `json:"release_id" gorm:"not null"`
	ImageURL  string         `json:"image_url"`
	Title     string         `json:"title"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}
