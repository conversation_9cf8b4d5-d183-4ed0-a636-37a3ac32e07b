package models

import (
	"time"

	"gorm.io/gorm"
)

// Release model
type Release struct {
	ID                  uint           `json:"id" gorm:"primaryKey"`
	Name                string         `json:"name" gorm:"not null"`
	PRDSignoff          *time.Time     `json:"prd_signoff"`
	PRDLink             string         `json:"prd_link"`
	TestStrategySignoff *time.Time     `json:"test_strategy_signoff"`
	TestStrategyLink    string         `json:"test_strategy_link"`
	ReleaseBranchOff    *time.Time     `json:"release_branch_off"`
	ReleaseCodeFreeze   *time.Time     `json:"release_code_freeze"`
	State               string         `json:"state"`
	TestReport          string         `json:"test_report"`
	Risk                string         `json:"risk"`
	RiskState           string         `json:"risk_state"`
	Softwares           string         `json:"softwares"`
	Docs                string         `json:"docs"`
	Lessons             string         `json:"lessons"`
	CreatedAt           time.Time      `json:"created_at"`
	UpdatedAt           time.Time      `json:"updated_at"`
	DeletedAt           gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	FeatureSummaries []FeatureSummary `json:"feature_summaries,omitempty"`
	CCBSummaries     []CCBSummary     `json:"ccb_summaries,omitempty"`
	RiskParts        []RiskPart       `json:"risk_parts,omitempty"`
	Qualities        []Quality        `json:"qualities,omitempty"`
}

// FeatureSummary model
type FeatureSummary struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	ReleaseID    uint           `json:"release_id" gorm:"not null"`
	Name         string         `json:"name"`
	Description  string         `json:"description"`
	Owner        string         `json:"owner"`
	State        string         `json:"state"`
	Started      *time.Time     `json:"started"`
	DevDone      *time.Time     `json:"dev_done"`
	Ended        *time.Time     `json:"ended"`
	DesignSpec   string         `json:"design_spec"`
	TestStrategy string         `json:"test_strategy"`
	TestReport   string         `json:"test_report"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// CCBSummary model
type CCBSummary struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	ReleaseID   uint           `json:"release_id" gorm:"not null"`
	Name        string         `json:"name"`
	Creator     string         `json:"creator"`
	Description string         `json:"description"`
	Created     *time.Time     `json:"created"`
	SignoffDate *time.Time     `json:"signoff_date"`
	State       string         `json:"state"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// RiskPart model
type RiskPart struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	ReleaseID        uint           `json:"release_id" gorm:"not null"`
	Team             string         `json:"team"`
	Type             string         `json:"type"`
	Description      string         `json:"description"`
	Probability      string         `json:"probability"`
	Impact           string         `json:"impact"`
	Status           string         `json:"status"`
	Migration        string         `json:"migration"`
	FallbackStrategy string         `json:"fallback_strategy"`
	Keeper           string         `json:"keeper"`
	Comments         string         `json:"comments"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// Quality model
type Quality struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	ReleaseID uint           `json:"release_id" gorm:"not null"`
	ImageURL  string         `json:"image_url"`
	Title     string         `json:"title"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}
