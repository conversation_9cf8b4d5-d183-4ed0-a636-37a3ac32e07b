package models

import (
	"database/sql/driver"
	"fmt"
	"strings"
	"time"

	"gorm.io/gorm"
)

// FeatureState constants for simplified JIRA-style workflow
const (
	FeatureStateBacklog    = "BACKLOG"
	FeatureStateNew        = "NEW"
	FeatureStateInProgress = "IN-PROGRESS"
	FeatureStateCancelled  = "CANCELLED"
	FeatureStateClosed     = "CLOSED"
	FeatureStateResolved   = "RESOLVED"
)

// ValidFeatureStates returns all valid feature states
func ValidFeatureStates() []string {
	return []string{
		FeatureStateBacklog,
		FeatureStateNew,
		FeatureStateInProgress,
		FeatureStateCancelled,
		FeatureStateClosed,
		FeatureStateResolved,
	}
}

// CustomDate handles date parsing for forms
type CustomDate struct {
	time.Time
}

func (cd *CustomDate) UnmarshalJSON(b []byte) error {
	s := strings.Trim(string(b), "\"")
	if s == "null" || s == "" {
		cd.Time = time.Time{}
		return nil
	}

	// Try different date formats
	formats := []string{
		"2006-01-02",
		"2006-01-02T15:04:05Z07:00",
		"2006-01-02T15:04:05Z",
		"2006-01-02T15:04:05",
	}

	for _, format := range formats {
		if t, err := time.Parse(format, s); err == nil {
			cd.Time = t
			return nil
		}
	}

	return fmt.Errorf("cannot parse %q as date", s)
}

func (cd CustomDate) MarshalJSON() ([]byte, error) {
	if cd.Time.IsZero() {
		return []byte("null"), nil
	}
	return []byte("\"" + cd.Time.Format("2006-01-02") + "\""), nil
}

func (cd CustomDate) Value() (driver.Value, error) {
	if cd.Time.IsZero() {
		return nil, nil
	}
	return cd.Time, nil
}

func (cd *CustomDate) Scan(value interface{}) error {
	if value == nil {
		cd.Time = time.Time{}
		return nil
	}
	cd.Time = value.(time.Time)
	return nil
}

// Release model
type Release struct {
	ID   uint   `json:"id" gorm:"primaryKey"`
	Name string `json:"name" gorm:"not null"`

	// MT0 - PRD阶段
	PRDSignoff *CustomDate `json:"prd_signoff" gorm:"type:date"`
	PRDLink    string      `json:"prd_link"`

	// MT1 - 测试策略阶段
	TestStrategySignoff *CustomDate `json:"test_strategy_signoff" gorm:"type:date"`
	TestStrategyLink    string      `json:"test_strategy_link"`
	ReleaseBranchOff    *CustomDate `json:"release_branch_off" gorm:"type:date"`

	// MT2 - 代码冻结阶段
	ReleaseCodeFreeze *CustomDate `json:"release_code_freeze" gorm:"type:date"`

	// MT3 - 发布阶段
	State            string `json:"state"`
	TestReport       string `json:"test_report"`
	RiskLink         string `json:"risk_link"` // 新增字段
	RiskState        string `json:"risk_state"`
	SoftwareDownload string `json:"software_download"` // 新增字段，重命名原softwares字段
	DocLink          string `json:"doc_link"`          // 新增字段，重命名原docs字段

	// MT4 - 总结阶段
	Lessons string `json:"lessons"`

	// 保留旧字段以向后兼容，标记为deprecated
	Risk      string `json:"risk,omitempty"`      // deprecated: 使用risk_link替代
	Softwares string `json:"softwares,omitempty"` // deprecated: 使用software_download替代
	Docs      string `json:"docs,omitempty"`      // deprecated: 使用doc_link替代

	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Relationships
	FeatureSummaries []FeatureSummary `json:"feature_summaries,omitempty"`
	CCBSummaries     []CCBSummary     `json:"ccb_summaries,omitempty"`
	RiskParts        []RiskPart       `json:"risk_parts,omitempty"`
	Qualities        []Quality        `json:"qualities,omitempty"`
}

// FeatureSummary model
// State field supports simplified JIRA-style workflow states:
// "BACKLOG", "NEW", "IN-PROGRESS", "CANCELLED", "CLOSED", "RESOLVED"
type FeatureSummary struct {
	ID           uint           `json:"id" gorm:"primaryKey"`
	ReleaseID    uint           `json:"release_id" gorm:"not null"`
	Name         string         `json:"name"`
	Description  string         `json:"description"`
	Owner        string         `json:"owner"`
	State        string         `json:"state"` // JIRA-style workflow states
	Started      *CustomDate    `json:"started" gorm:"type:date"`
	DevDone      *CustomDate    `json:"dev_done" gorm:"type:date"`
	Ended        *CustomDate    `json:"ended" gorm:"type:date"`
	DesignSpec   string         `json:"design_spec"`
	TestStrategy string         `json:"test_strategy"`
	TestReport   string         `json:"test_report"`
	CreatedAt    time.Time      `json:"created_at"`
	UpdatedAt    time.Time      `json:"updated_at"`
	DeletedAt    gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// CCBSummary model
type CCBSummary struct {
	ID          uint           `json:"id" gorm:"primaryKey"`
	ReleaseID   uint           `json:"release_id" gorm:"not null"`
	Name        string         `json:"name"`
	Creator     string         `json:"creator"`
	Description string         `json:"description"`
	Created     *CustomDate    `json:"created" gorm:"type:date"`
	SignoffDate *CustomDate    `json:"signoff_date" gorm:"type:date"`
	State       string         `json:"state"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// RiskPart model
type RiskPart struct {
	ID               uint           `json:"id" gorm:"primaryKey"`
	ReleaseID        uint           `json:"release_id" gorm:"not null"`
	Team             string         `json:"team"`
	Type             string         `json:"type"`
	Description      string         `json:"description"`
	Probability      string         `json:"probability"`
	Impact           string         `json:"impact"`
	Status           string         `json:"status"`
	Migration        string         `json:"migration"`
	FallbackStrategy string         `json:"fallback_strategy"`
	Keeper           string         `json:"keeper"`
	Comments         string         `json:"comments"`
	CreatedAt        time.Time      `json:"created_at"`
	UpdatedAt        time.Time      `json:"updated_at"`
	DeletedAt        gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}

// Quality model
type Quality struct {
	ID        uint           `json:"id" gorm:"primaryKey"`
	ReleaseID uint           `json:"release_id" gorm:"not null"`
	ImageURL  string         `json:"image_url"`
	Title     string         `json:"title"`
	CreatedAt time.Time      `json:"created_at"`
	UpdatedAt time.Time      `json:"updated_at"`
	DeletedAt gorm.DeletedAt `json:"-" gorm:"index"`

	// Foreign key relationship
	Release Release `json:"-" gorm:"foreignKey:ReleaseID"`
}
