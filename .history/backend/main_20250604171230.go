package main

import (
	"fmt"
	"log"
	"os"

	"release-management-backend/controllers"
	"release-management-backend/models"

	"github.com/gin-contrib/cors"
	"github.com/gin-gonic/gin"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func initDatabase() *gorm.DB {
	host := getEnv("DB_HOST", "localhost")
	port := getEnv("DB_PORT", "5432")
	user := getEnv("DB_USER", "postgres")
	password := getEnv("DB_PASSWORD", "postgres")
	dbname := getEnv("DB_NAME", "release_management")

	dsn := fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable TimeZone=UTC",
		host, user, password, dbname, port)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	// Auto-migrate the schema
	err = db.AutoMigrate(
		&models.Release{},
		&models.FeatureSummary{},
		&models.CCBSummary{},
		&models.RiskPart{},
		&models.Quality{},
	)
	if err != nil {
		log.Fatal("Failed to migrate database:", err)
	}

	return db
}

func getEnv(key, defaultValue string) string {
	if value := os.Getenv(key); value != "" {
		return value
	}
	return defaultValue
}

func setupRoutes(db *gorm.DB) *gin.Engine {
	r := gin.Default()

	// CORS middleware
	config := cors.DefaultConfig()
	config.AllowOrigins = []string{"http://localhost:3000"}
	config.AllowMethods = []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"}
	config.AllowHeaders = []string{"Origin", "Content-Type", "Content-Length", "Accept-Encoding", "X-CSRF-Token", "Authorization"}
	r.Use(cors.New(config))

	// Initialize controllers
	releaseController := controllers.NewReleaseController(db)
	featureController := controllers.NewFeatureController(db)

	// API routes
	api := r.Group("/api")
	{
		// Release routes
		releases := api.Group("/releases")
		{
			releases.GET("", releaseController.GetReleases)
			releases.POST("", releaseController.CreateRelease)
			releases.GET("/:id", releaseController.GetRelease)
			releases.PUT("/:id", releaseController.UpdateRelease)
			releases.DELETE("/:id", releaseController.DeleteRelease)

			// Feature routes under specific release
			releases.GET("/:id/features", featureController.GetFeatures)
			releases.POST("/:id/features", featureController.CreateFeature)
		}

		// Feature routes
		features := api.Group("/features")
		{
			features.PUT("/:id", featureController.UpdateFeature)
			features.DELETE("/:id", featureController.DeleteFeature)
		}
	}

	return r
}

func main() {
	// Initialize database
	db := initDatabase()

	// Setup routes
	r := setupRoutes(db)

	// Start server
	port := getEnv("PORT", "8080")
	log.Printf("Server starting on port %s", port)
	log.Fatal(r.Run(":" + port))
}
