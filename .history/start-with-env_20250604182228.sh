#!/bin/bash

# Load environment variables from .env file
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | xargs)
    echo "✅ Loaded environment variables from .env"
else
    echo "⚠️  .env file not found, using default values"
fi

echo "🔧 Configuration:"
echo "   Server IP: ${SERVER_IP:-***************}"
echo "   Frontend Port: ${FRONTEND_PORT:-3000}"
echo "   Backend Port: ${BACKEND_PORT:-8080}"
echo "   API Base URL: ${VITE_API_BASE_URL:-http://***************:8080}"

# Start backend in background
echo "🚀 Starting backend service..."
cd backend
PORT=${BACKEND_PORT:-8080} GIN_MODE=${GIN_MODE:-debug} go run main.go &
BACKEND_PID=$!
cd ..

# Wait a moment for backend to start
sleep 3

# Start frontend in background
echo "🚀 Starting frontend service..."
cd frontend
VITE_API_BASE_URL=${VITE_API_BASE_URL} npm run dev -- --host 0.0.0.0 --port ${FRONTEND_PORT:-3000} &
FRONTEND_PID=$!
cd ..

echo "✅ Services started!"
echo "   Backend: http://${SERVER_IP:-***************}:${BACKEND_PORT:-8080}"
echo "   Frontend: http://${SERVER_IP:-***************}:${FRONTEND_PORT:-3000}"
echo ""
echo "Press Ctrl+C to stop all services"

# Function to cleanup on exit
cleanup() {
    echo ""
    echo "🛑 Stopping services..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "✅ All services stopped"
    exit 0
}

# Set trap to cleanup on script termination
trap cleanup SIGINT SIGTERM

# Wait for background processes
wait
