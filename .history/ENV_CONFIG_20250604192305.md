# Environment Configuration

This project uses environment variables for configuration management. All configuration is centralized in the `.env` file.

## Setup

1. Copy the example environment file:

   ```bash
   cp .env.example .env
   ```

2. Edit `.env` file with your specific values:

   ```bash
   nano .env
   ```

## Environment Variables

### Network Configuration

- `SERVER_HOST=0.0.0.0` - Server bind address (for Docker)
- `SERVER_IP=***************` - Your server IP address
- `FRONTEND_PORT=3000` - Frontend development server port
- `BACKEND_PORT=8080` - Backend API server port

### API Configuration

- `VITE_API_BASE_URL=http://***************:8080` - Frontend API endpoint

### Database Configuration

- `DB_HOST=localhost` - Database host (for local development)
- `DB_PORT=5432` - Database port
- `DB_USER=postgres` - Database username
- `DB_PASSWORD=postgres` - Database password
- `DB_NAME=release_management` - Database name

### Docker Database Configuration

- `POSTGRES_USER=postgres` - PostgreSQL Docker container user
- `POSTGRES_PASSWORD=postgres` - PostgreSQL Docker container password
- `POSTGRES_DB=release_management` - PostgreSQL Docker container database

### Environment Settings

- `NODE_ENV=development` - Node.js environment mode
- `GIN_MODE=debug` - Gin framework mode (debug/release)

## Usage

### Local Development

```bash
# Start services with environment variables
./start-with-env.sh
```

### Docker Deployment

```bash
# Docker Compose automatically reads .env file
docker-compose up --build
```

### Manual Start

```bash
# Load environment variables
source .env

# Start backend
cd backend
PORT=$BACKEND_PORT GIN_MODE=$GIN_MODE go run main.go

# Start frontend (in another terminal)
cd frontend
VITE_API_BASE_URL=$VITE_API_BASE_URL npm run dev -- --host 0.0.0.0 --port $FRONTEND_PORT
```

## Network Access

To allow team access, set `SERVER_IP` to your machine's network IP address:

1. Find your IP:

   ```bash
   ip addr show | grep 'inet ' | grep -v '127.0.0.1'
   ```

2. Update `.env`:

   ```
   SERVER_IP=192.168.x.x  # Your actual IP
   VITE_API_BASE_URL=http://192.168.x.x:8080
   ```

3. Restart services:

   ```bash
   ./start-with-env.sh
   ```

## Troubleshooting

### Connection Issues

- Ensure `SERVER_IP` matches your actual network IP
- Check firewall settings for ports 3000 and 8080
- Verify backend is accessible: `curl http://$SERVER_IP:$BACKEND_PORT/api/releases`

### Docker Issues

- Environment variables not loading: Check `.env` file exists in project root
- Port conflicts: Change `FRONTEND_PORT` and `BACKEND_PORT` in `.env`

### Frontend API Errors

- Check `VITE_API_BASE_URL` matches backend address
- Verify backend CORS settings allow your frontend domain
