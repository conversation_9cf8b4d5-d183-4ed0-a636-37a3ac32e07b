#!/bin/bash

# Release Management System Startup Script
# You can modify the IP address here
export SERVER_IP="***************"

echo "🚀 Starting Release Management System"
echo "📍 Server IP: $SERVER_IP"
echo ""

# Kill existing processes
echo "🧹 Cleaning up existing processes..."
pkill -f "go run main.go" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true

# Wait a moment
sleep 2

# Start backend
echo "🔧 Starting backend server..."
cd backend
export DB_HOST=localhost
export DB_PORT=5432
export DB_USER=postgres
export DB_PASSWORD=postgres
export DB_NAME=release_management
export SERVER_HOST=0.0.0.0
export PORT=8080

nohup go run main.go >../backend.log 2>&1 &
BACKEND_PID=$!
echo "   Backend started (PID: $BACKEND_PID)"
echo "   Logs: backend.log"

# Wait for backend to start
sleep 3

# Start frontend
echo "🎨 Starting frontend server..."
cd ../frontend
export VITE_API_BASE_URL="http://$SERVER_IP:8080"

nohup npm run dev >../frontend.log 2>&1 &
FRONTEND_PID=$!
echo "   Frontend started (PID: $FRONTEND_PID)"
echo "   Logs: frontend.log"

# Wait for frontend to start
sleep 3

echo ""
echo "✅ Services started successfully!"
echo ""
echo "🌐 Access URLs:"
echo "   Frontend: http://localhost:3000"
echo "   Frontend: http://$SERVER_IP:3000"
echo "   Backend API: http://localhost:8080/api"
echo "   Backend API: http://$SERVER_IP:8080/api"
echo ""
echo "📋 Process IDs:"
echo "   Backend: $BACKEND_PID"
echo "   Frontend: $FRONTEND_PID"
echo ""
echo "🛑 To stop services: pkill -f 'go run main.go' && pkill -f 'npm run dev'"
echo "📜 View logs: tail -f backend.log frontend.log"
