#!/bin/bash

# Release Management System Startup Script
# This script loads configuration from .env and starts all services

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 Release Management System Startup${NC}"
echo "=================================================="

# Load environment variables from .env file
if [ -f .env ]; then
    export $(cat .env | grep -v '^#' | grep -v '^$' | xargs)
    echo -e "${GREEN}✅ Loaded environment variables from .env${NC}"
else
    echo -e "${YELLOW}⚠️  .env file not found, using default values${NC}"
    # Set default values
    export HOST=***************
    export FRONTEND_PORT=3000
    export BACKEND_PORT=8080
    export DB_HOST=localhost
    export DB_PORT=5432
    export DB_USER=postgres
    export DB_PASSWORD=postgres
    export DB_NAME=release_management
    export SERVER_HOST=0.0.0.0
    export GIN_MODE=debug
    export VITE_API_BASE_URL=http://***************:8080
fi

echo ""
echo -e "${BLUE}🔧 Configuration:${NC}"
echo "   Host: ${HOST}"
echo "   Frontend Port: ${FRONTEND_PORT}"
echo "   Backend Port: ${BACKEND_PORT}"
echo "   Database: ${DB_USER}@${DB_HOST}:${DB_PORT}/${DB_NAME}"
echo "   API Base URL: ${VITE_API_BASE_URL}"
echo ""

# Function to cleanup on exit
cleanup() {
    echo ""
    echo -e "${YELLOW}🛑 Stopping services...${NC}"
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    pkill -f "go run main.go" 2>/dev/null
    pkill -f "npm run dev" 2>/dev/null
    echo -e "${GREEN}✅ All services stopped${NC}"
    exit 0
}

# Set trap to cleanup on script termination
trap cleanup SIGINT SIGTERM

# Kill existing processes
echo -e "${YELLOW}🧹 Cleaning up existing processes...${NC}"
pkill -f "go run main.go" 2>/dev/null || true
pkill -f "npm run dev" 2>/dev/null || true
pkill -f "vite" 2>/dev/null || true

# Wait a moment
sleep 2

# Start backend
echo -e "${BLUE}🔧 Starting backend server...${NC}"
cd backend

# Export all database and server environment variables
export PORT=$BACKEND_PORT

nohup go run main.go >../backend.log 2>&1 &
BACKEND_PID=$!

echo -e "${GREEN}   ✅ Backend started (PID: $BACKEND_PID)${NC}"
echo "   📜 Logs: backend.log"

cd ..

# Wait for backend to start
echo "   ⏳ Waiting for backend to initialize..."
sleep 5

# Check if backend is running
if kill -0 $BACKEND_PID 2>/dev/null; then
    echo -e "${GREEN}   ✅ Backend is running successfully${NC}"
else
    echo -e "${RED}   ❌ Backend failed to start. Check backend.log for details.${NC}"
    cat backend.log | tail -10
    exit 1
fi

# Start frontend
echo -e "${BLUE}🎨 Starting frontend server...${NC}"
cd frontend

nohup npm run dev -- --host 0.0.0.0 --port $FRONTEND_PORT >../frontend.log 2>&1 &
FRONTEND_PID=$!

echo -e "${GREEN}   ✅ Frontend started (PID: $FRONTEND_PID)${NC}"
echo "   📜 Logs: frontend.log"

cd ..

# Wait for frontend to start
echo "   ⏳ Waiting for frontend to initialize..."
sleep 5

# Check if frontend is running
if kill -0 $FRONTEND_PID 2>/dev/null; then
    echo -e "${GREEN}   ✅ Frontend is running successfully${NC}"
else
    echo -e "${RED}   ❌ Frontend failed to start. Check frontend.log for details.${NC}"
    cat frontend.log | tail -10
    exit 1
fi

echo ""
echo "=================================================="
echo -e "${GREEN}✅ All services started successfully!${NC}"
echo "=================================================="
echo ""
echo -e "${BLUE}🌐 Access URLs:${NC}"
echo "   Frontend (Local):  http://localhost:${FRONTEND_PORT}"
echo "   Frontend (Network): http://${HOST}:${FRONTEND_PORT}"
echo "   Backend API (Local): http://localhost:${BACKEND_PORT}/api"
echo "   Backend API (Network): http://${HOST}:${BACKEND_PORT}/api"
echo ""
echo -e "${BLUE}📋 Process Information:${NC}"
echo "   Backend PID: $BACKEND_PID"
echo "   Frontend PID: $FRONTEND_PID"
echo ""
echo -e "${BLUE}📜 Useful Commands:${NC}"
echo "   View logs: tail -f backend.log frontend.log"
echo "   Stop services: pkill -f 'go run main.go' && pkill -f 'npm run dev'"
echo "   Or press Ctrl+C to stop all services"
echo ""
echo -e "${YELLOW}⏳ Services are running... Press Ctrl+C to stop${NC}"

# Wait for background processes or user interrupt
wait
