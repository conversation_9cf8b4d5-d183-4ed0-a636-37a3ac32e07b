---
description: 
globs: 
alwaysApply: false
---
# API Patterns and Conventions

This guide explains the API patterns and conventions used in the Release Management System backend.

## API Structure

The backend follows RESTful API conventions with the following base URL pattern:
- Base URL: `/api`
- All endpoints are prefixed with `/api`

## Resource Controllers

Each major entity has its own controller in [backend/controllers/](mdc:backend/controllers):

### Release Controller
File: [backend/controllers/release_controller.go](mdc:backend/controllers/release_controller.go)
- `GET /api/releases` - List all releases
- `GET /api/releases/:id` - Get specific release
- `POST /api/releases` - Create new release
- `PUT /api/releases/:id` - Update release
- `DELETE /api/releases/:id` - Delete release

### Feature Controller  
File: [backend/controllers/feature_controller.go](mdc:backend/controllers/feature_controller.go)
- `GET /api/releases/:releaseId/features` - List features for a release
- `POST /api/releases/:releaseId/features` - Create feature in release
- `PUT /api/features/:id` - Update feature
- `DELETE /api/features/:id` - Delete feature

### CCB Controller
File: [backend/controllers/ccb_controller.go](mdc:backend/controllers/ccb_controller.go)
- Change Control Board management endpoints
- Handles change request approval workflows

### Risk Controller
File: [backend/controllers/risk_controller.go](mdc:backend/controllers/risk_controller.go)  
- Risk assessment and mitigation endpoints
- Risk probability and impact tracking

## Model Structure

Database models are defined in [backend/models/](mdc:backend/models) using GORM conventions:
- Models use struct tags for database mapping
- Relationships defined with GORM associations
- Database migrations handled automatically

## Error Handling

The API uses standard HTTP status codes:
- 200: Success
- 201: Created
- 400: Bad Request  
- 404: Not Found
- 500: Internal Server Error

## Server Configuration

Main server setup in [backend/main.go](mdc:backend/main.go):
- Gin router configuration
- CORS middleware setup
- Database connection initialization
- Route registration
