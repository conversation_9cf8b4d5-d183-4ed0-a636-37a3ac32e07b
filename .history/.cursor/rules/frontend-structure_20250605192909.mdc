---
description: 
globs: 
alwaysApply: false
---
# Frontend Structure Guide (Vue 3)

The frontend is a Vue 3 application using modern tooling and Material Design components.

## Project Configuration

- **Build Tool**: [frontend/vite.config.js](mdc:frontend/vite.config.js) - Vite configuration
- **Dependencies**: [frontend/package.json](mdc:frontend/package.json) - Project dependencies
- **Code Style**: [frontend/.eslintrc.json](mdc:frontend/.eslintrc.json) and [frontend/.prettierrc](mdc:frontend/.prettierrc)

## Application Structure

- **Entry Point**: [frontend/src/main.js](mdc:frontend/src/main.js) - Vue app initialization
- **Root Component**: [frontend/src/App.vue](mdc:frontend/src/App.vue) - Main application component
- **Global Styles**: [frontend/src/style.css](mdc:frontend/src/style.css) - Application-wide styles

## Key Directories

- **Components**: [frontend/src/components/](mdc:frontend/src/components) - Reusable Vue components
- **Views**: [frontend/src/views/](mdc:frontend/src/views) - Page-level components
- **Stores**: [frontend/src/stores/](mdc:frontend/src/stores) - Pinia state management
- **Router**: [frontend/src/router/](mdc:frontend/src/router) - Vue Router configuration
- **Plugins**: [frontend/src/plugins/](mdc:frontend/src/plugins) - Vue plugins (likely Vuetify)

## Development Notes

- Uses Vuetify 3 for Material Design components
- Pinia for state management
- Vite for fast development and building
- ESLint + Prettier for code quality
