import { createRouter, createWebHistory } from 'vue-router'
import Dashboard from '../views/Dashboard.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'dashboard',
      component: Dashboard,
    },
    {
      path: '/releases/:releaseId/:tab?',
      name: 'release-details',
      component: Dashboard,
      props: (route) => ({
        releaseId: parseInt(route.params.releaseId),
        tab: route.params.tab || 'overview',
      }),
    },
  ],
})

export default router
