<template>
  <div class="d-flex" style="height: 100vh;">
    <!-- Left Sidebar -->
    <div style="position: relative;">
      <v-navigation-drawer permanent :width="sidebarCollapsed ? 60 : 300" class="elevation-2">
        <div class="d-flex justify-space-between align-center pa-2">
          <div v-if="!sidebarCollapsed" class="text-h6 font-weight-bold">Releases</div>
          <v-btn
            v-if="!sidebarCollapsed"
            color="primary"
            size="small"
            @click="showCreateDialog = true"
          >
            <v-icon>mdi-plus</v-icon>
            New Release
          </v-btn>
        </div>

        <!-- Toggle Button positioned at the edge -->
        <v-btn
          icon
          @click="sidebarCollapsed = !sidebarCollapsed"
          size="small"
          class="toggle-btn"
          :style="{
            position: 'absolute',
            right: sidebarCollapsed ? '8px' : '-12px',
            top: '8px',
            zIndex: 1001,
            backgroundColor: 'white',
            boxShadow: '0 2px 4px rgba(0,0,0,0.2)',
          }"
        >
          <v-icon>{{ sidebarCollapsed ? 'mdi-chevron-right' : 'mdi-chevron-left' }}</v-icon>
        </v-btn>

        <v-divider></v-divider>

        <v-list v-if="!loading">
          <v-list-item
            v-for="release in releases"
            :key="release.id"
            @click="selectRelease(release)"
            :active="currentRelease?.id === release.id"
            class="mb-1"
          >
            <template v-if="!sidebarCollapsed">
              <v-list-item-title>{{ release.name }}</v-list-item-title>
              <v-list-item-subtitle>{{ release.state || 'No Status' }}</v-list-item-subtitle>
            </template>
            <template v-else>
              <v-tooltip activator="parent" location="end">
                {{ release.name }}
              </v-tooltip>
              <v-icon>mdi-rocket-launch</v-icon>
            </template>
          </v-list-item>
        </v-list>

        <div v-if="loading" class="text-center pa-4">
          <v-progress-circular indeterminate></v-progress-circular>
        </div>
      </v-navigation-drawer>
    </div>

    <!-- Main Content Area -->
    <div class="flex-grow-1" style="overflow-y: auto;">
      <div class="pa-2">
          <div v-if="!currentRelease" class="text-center pa-8">
            <v-icon size="64" color="grey">mdi-rocket-launch-outline</v-icon>
            <h2 class="text-h5 mt-4 text-grey">Select a release to view details</h2>
            <p class="text-grey">Choose a release from the sidebar or create a new one</p>
          </div>

          <div v-else>
            <!-- Release Header -->
            <div class="d-flex justify-space-between align-center mb-4">
              <div>
                <h1 class="text-h4">{{ currentRelease.name }}</h1>
                <v-chip :color="getStatusColor(currentRelease.state)" class="mt-2">
                  {{ currentRelease.state || 'No Status' }}
                </v-chip>
              </div>
              <div>
                <v-btn color="primary" variant="outlined" class="mr-2" @click="editRelease">
                  <v-icon>mdi-pencil</v-icon>
                  Edit
                </v-btn>
                <v-btn color="error" variant="outlined" @click="confirmDelete = true">
                  <v-icon>mdi-delete</v-icon>
                  Delete
                </v-btn>
              </div>
            </div>

            <!-- Release Details Tabs -->
            <v-tabs v-model="currentTab" class="mb-4">
              <v-tab value="overview">Overview</v-tab>
              <v-tab value="features">Feature Summary</v-tab>
              <v-tab value="ccb">CCB Summary</v-tab>
              <v-tab value="risks">Risk Part</v-tab>
              <v-tab value="quality">Quality</v-tab>
            </v-tabs>

            <v-window v-model="currentTab">
              <!-- Overview Tab -->
              <v-window-item value="overview">
                <ReleaseOverview :release="currentRelease" />
              </v-window-item>

              <!-- Features Tab -->
              <v-window-item value="features">
                <FeatureSummary
                  :release-id="currentRelease.id"
                  :features="currentRelease.feature_summaries || []"
                />
              </v-window-item>

              <!-- CCB Tab -->
              <v-window-item value="ccb">
                <CCBSummary
                  :release-id="currentRelease.id"
                  :ccb-summaries="currentRelease.ccb_summaries || []"
                />
              </v-window-item>

              <!-- Risks Tab -->
              <v-window-item value="risks">
                <RiskPart
                  :release-id="currentRelease.id"
                  :risks="currentRelease.risk_parts || []"
                />
              </v-window-item>

              <!-- Quality Tab -->
              <v-window-item value="quality">
                <QualityTab
                  :release-id="currentRelease.id"
                  :qualities="currentRelease.qualities || []"
                />
              </v-window-item>
            </v-window>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Release Dialog -->
    <ReleaseDialog v-model="showCreateDialog" :release="editingRelease" @save="handleSaveRelease" />

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="confirmDelete" max-width="400">
      <v-card>
        <v-card-title>Confirm Delete</v-card-title>
        <v-card-text>
          Are you sure you want to delete this release? This action cannot be undone.
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="confirmDelete = false">Cancel</v-btn>
          <v-btn color="error" @click="handleDeleteRelease">Delete</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-container>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { storeToRefs } from 'pinia'
import { useReleaseStore } from '@/stores/release'
import ReleaseOverview from '@/components/ReleaseOverview.vue'
import FeatureSummary from '@/components/FeatureSummary.vue'
import CCBSummary from '@/components/CCBSummary.vue'
import RiskPart from '@/components/RiskPart.vue'
import QualityTab from '@/components/QualityTab.vue'
import ReleaseDialog from '@/components/ReleaseDialog.vue'

const releaseStore = useReleaseStore()
const { releases, currentRelease, loading } = storeToRefs(releaseStore)

const sidebarCollapsed = ref(false)
const currentTab = ref('overview')
const showCreateDialog = ref(false)
const editingRelease = ref(null)
const confirmDelete = ref(false)

onMounted(() => {
  releaseStore.fetchReleases()
})

function selectRelease(release) {
  releaseStore.fetchRelease(release.id)
}

function editRelease() {
  editingRelease.value = { ...currentRelease.value }
  showCreateDialog.value = true
}

async function handleSaveRelease(releaseData) {
  try {
    if (editingRelease.value?.id) {
      await releaseStore.updateRelease(editingRelease.value.id, releaseData)
    } else {
      const newRelease = await releaseStore.createRelease(releaseData)
      // Auto-select the newly created release
      releaseStore.setCurrentRelease(newRelease)
    }
    showCreateDialog.value = false
    editingRelease.value = null
  } catch (error) {
    console.error('Error saving release:', error)
  }
}

async function handleDeleteRelease() {
  try {
    await releaseStore.deleteRelease(currentRelease.value.id)
    confirmDelete.value = false
  } catch (error) {
    console.error('Error deleting release:', error)
  }
}

function getStatusColor(status) {
  const statusColors = {
    planning: 'blue',
    development: 'orange',
    testing: 'purple',
    release: 'green',
    completed: 'teal',
    cancelled: 'red',
  }
  return statusColors[status?.toLowerCase()] || 'grey'
}
</script>

<style scoped>
.v-navigation-drawer {
  border-right: 1px solid rgba(0, 0, 0, 0.12);
}
</style>
