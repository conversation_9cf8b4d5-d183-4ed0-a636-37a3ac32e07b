<template>
  <div class="d-flex" style="height: 100vh">
    <!-- Left Sidebar -->
    <div
      style="position: relative; display: flex; flex-direction: column"
      :style="{ width: sidebarCollapsed ? '60px' : '300px' }"
    >
      <!-- Sidebar Header -->
      <div
        class="d-flex align-center justify-space-between pa-3"
        style="border-bottom: 1px solid rgba(0, 0, 0, 0.12); background-color: #f5f5f5"
      >
        <v-btn icon @click="sidebarCollapsed = !sidebarCollapsed" size="small" variant="text">
          <v-icon>{{ sidebarCollapsed ? 'mdi-chevron-right' : 'mdi-menu' }}</v-icon>
        </v-btn>

        <div v-if="!sidebarCollapsed" class="d-flex align-center flex-grow-1 ml-2">
          <div class="text-h6 font-weight-bold flex-grow-1">Release Management</div>
        </div>
      </div>

      <!-- Sidebar Content -->
      <div
        class="flex-grow-1"
        style="
          background-color: #f5f5f5;
          border-right: 1px solid rgba(0, 0, 0, 0.12);
          overflow-y: auto;
        "
      >
        <!-- Expanded Sidebar -->
        <v-list
          v-if="!loading && !sidebarCollapsed"
          class="pa-0"
          style="background-color: transparent"
        >
          <!-- Release Summary Menu Item -->
          <v-list-item @click="showReleaseSummary" :active="currentView === 'summary'" class="mb-1">
            <template v-slot:prepend>
              <v-icon>mdi-chart-box</v-icon>
            </template>
            <v-list-item-title>Release Summary</v-list-item-title>
          </v-list-item>

          <!-- Releases Group -->
          <div>
            <!-- Releases Header -->
            <v-list-item @click="releasesGroupOpen = !releasesGroupOpen" class="mb-1">
              <template v-slot:prepend>
                <v-icon>mdi-rocket-launch</v-icon>
              </template>
              <v-list-item-title>Releases</v-list-item-title>
              <template v-slot:append>
                <v-btn
                  icon="mdi-plus"
                  size="x-small"
                  variant="text"
                  @click.stop="showCreateDialog = true"
                ></v-btn>
                <v-icon>{{ releasesGroupOpen ? 'mdi-chevron-up' : 'mdi-chevron-down' }}</v-icon>
              </template>
            </v-list-item>

            <!-- Releases List -->
            <v-expand-transition>
              <div v-show="releasesGroupOpen">
                <v-list-item
                  v-for="release in releases"
                  :key="release.id"
                  @click="selectRelease(release)"
                  :active="currentRelease?.id === release.id && currentView === 'details'"
                  class="ml-4"
                >
                  <v-list-item-title>{{ release.name }}</v-list-item-title>
                  <v-list-item-subtitle>{{ release.state || 'No Status' }}</v-list-item-subtitle>
                </v-list-item>
              </div>
            </v-expand-transition>
          </div>
        </v-list>

        <!-- Collapsed Sidebar -->
        <v-list
          v-if="!loading && sidebarCollapsed"
          class="pa-0"
          style="background-color: transparent"
        >
          <!-- Release Summary Menu Item -->
          <v-list-item @click="showReleaseSummary" :active="currentView === 'summary'" class="mb-1">
            <v-tooltip activator="parent" location="end">Release Summary</v-tooltip>
            <v-icon>mdi-chart-box</v-icon>
          </v-list-item>

          <!-- Collapsed Releases -->
          <v-list-item
            v-for="release in releases"
            :key="release.id"
            @click="selectRelease(release)"
            :active="currentRelease?.id === release.id && currentView === 'details'"
            class="mb-1"
          >
            <v-tooltip activator="parent" location="end">
              {{ release.name }}
            </v-tooltip>
            <v-icon>mdi-rocket-launch</v-icon>
          </v-list-item>
        </v-list>

        <div v-if="loading" class="text-center pa-4">
          <v-progress-circular indeterminate></v-progress-circular>
        </div>
      </div>
    </div>

    <!-- Main Content Area -->
    <div class="flex-grow-1" style="display: flex; flex-direction: column; background-color: white">
      <!-- Content Header (Show different headers based on current view) -->
      <div
        v-if="currentView === 'details' && currentRelease"
        class="d-flex align-center justify-space-between pa-4"
        style="border-bottom: 1px solid rgba(0, 0, 0, 0.12)"
      >
        <div>
          <h1 class="text-h4">{{ currentRelease.name }}</h1>
          <v-chip :color="getStatusColor(currentRelease.state)" class="mt-1" size="small">
            {{ currentRelease.state || 'No Status' }}
          </v-chip>
        </div>
        <div>
          <v-btn color="primary" variant="outlined" class="mr-2" @click="editRelease">
            <v-icon>mdi-pencil</v-icon>
            Edit
          </v-btn>
          <v-btn color="error" variant="outlined" @click="confirmDelete = true">
            <v-icon>mdi-delete</v-icon>
            Delete
          </v-btn>
        </div>
      </div>

      <!-- Main Content -->
      <div class="flex-grow-1" style="overflow-y: auto">
        <div class="pa-4">
          <!-- Release Summary View -->
          <div v-if="currentView === 'summary'">
            <ReleaseSummary
              :releases="releases"
              :loading="loading"
              @select-release="selectReleaseFromSummary"
              @create-release="showCreateDialog = true"
              @edit-release="editReleaseFromSummary"
              @delete-release="deleteReleaseFromSummary"
            />
          </div>

          <!-- Release Details View -->
          <div v-else-if="currentView === 'details' && currentRelease">
            <!-- Release Details Tabs -->
            <v-tabs v-model="currentTab" class="mb-4">
              <v-tab value="overview">Overview</v-tab>
              <v-tab value="features">Feature Summary</v-tab>
              <v-tab value="ccb">CCB Summary</v-tab>
              <v-tab value="risks">Risk Part</v-tab>
              <v-tab value="quality">Quality</v-tab>
            </v-tabs>

            <v-window v-model="currentTab">
              <!-- Overview Tab -->
              <v-window-item value="overview">
                <ReleaseOverview :release="currentRelease" />
              </v-window-item>

              <!-- Features Tab -->
              <v-window-item value="features">
                <FeatureSummary
                  :release-id="currentRelease.id"
                  :features="currentRelease.feature_summaries || []"
                />
              </v-window-item>

              <!-- CCB Tab -->
              <v-window-item value="ccb">
                <CCBSummary
                  :release-id="currentRelease.id"
                  :ccb-summaries="currentRelease.ccb_summaries || []"
                  @refresh="refreshCurrentRelease"
                />
              </v-window-item>

              <!-- Risks Tab -->
              <v-window-item value="risks">
                <RiskPart
                  :release-id="currentRelease.id"
                  :risks="currentRelease.risk_parts || []"
                  @refresh="refreshCurrentRelease"
                />
              </v-window-item>

              <!-- Quality Tab -->
              <v-window-item value="quality">
                <QualityTab
                  :release-id="currentRelease.id"
                  :qualities="currentRelease.qualities || []"
                />
              </v-window-item>
            </v-window>
          </div>

          <!-- Default Empty State -->
          <div v-else class="text-center pa-8">
            <v-icon size="64" color="grey">mdi-rocket-launch-outline</v-icon>
            <h2 class="text-h5 mt-4 text-grey">Welcome to Release Management</h2>
            <p class="text-grey">
              Select "Release Summary" to view all releases or choose a specific release from the
              sidebar
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Create/Edit Release Dialog -->
    <ReleaseDialog v-model="showCreateDialog" :release="editingRelease" @save="handleSaveRelease" />

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="confirmDelete" max-width="400">
      <v-card>
        <v-card-title>Confirm Delete</v-card-title>
        <v-card-text>
          Are you sure you want to delete this release? This action cannot be undone.
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="confirmDelete = false">Cancel</v-btn>
          <v-btn color="error" @click="handleDeleteRelease">Delete</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch, defineProps } from 'vue'
import { storeToRefs } from 'pinia'
import { useReleaseStore } from '@/stores/release'
import { useRoute, useRouter } from 'vue-router'
import ReleaseSummary from '@/components/ReleaseSummary.vue'
import ReleaseOverview from '@/components/ReleaseOverview.vue'
import FeatureSummary from '@/components/FeatureSummary.vue'
import CCBSummary from '@/components/CCBSummary.vue'
import RiskPart from '@/components/RiskPart.vue'
import QualityTab from '@/components/QualityTab.vue'
import ReleaseDialog from '@/components/ReleaseDialog.vue'

const releaseStore = useReleaseStore()
const { releases, currentRelease, loading } = storeToRefs(releaseStore)

const sidebarCollapsed = ref(false)
const currentTab = ref('overview')
const currentView = ref('summary') // 'summary' or 'details'
const showCreateDialog = ref(false)
const editingRelease = ref(null)
const confirmDelete = ref(false)
const releasesGroupOpen = ref(true) // Keep releases group open by default

onMounted(() => {
  releaseStore.fetchReleases()
})

function showReleaseSummary() {
  currentView.value = 'summary'
  releaseStore.clearCurrentRelease()
  // Keep releases group open when viewing summary
  releasesGroupOpen.value = true
}

function selectRelease(release) {
  currentView.value = 'details'
  releaseStore.fetchRelease(release.id)
  // Ensure releases group stays open
  releasesGroupOpen.value = true
}

function selectReleaseFromSummary(release) {
  currentView.value = 'details'
  releaseStore.fetchRelease(release.id)
  // Ensure releases group stays open
  releasesGroupOpen.value = true
}

function editRelease() {
  editingRelease.value = { ...currentRelease.value }
  showCreateDialog.value = true
}

function editReleaseFromSummary(release) {
  editingRelease.value = { ...release }
  showCreateDialog.value = true
}

function deleteReleaseFromSummary(release) {
  releaseStore.setCurrentRelease(release)
  confirmDelete.value = true
}

async function handleSaveRelease(releaseData) {
  try {
    if (editingRelease.value?.id) {
      await releaseStore.updateRelease(editingRelease.value.id, releaseData)
    } else {
      const newRelease = await releaseStore.createRelease(releaseData)
      // Auto-select the newly created release
      releaseStore.setCurrentRelease(newRelease)
    }
    showCreateDialog.value = false
    editingRelease.value = null
  } catch (error) {
    console.error('Error saving release:', error)
  }
}

async function handleDeleteRelease() {
  try {
    await releaseStore.deleteRelease(currentRelease.value.id)
    confirmDelete.value = false
  } catch (error) {
    console.error('Error deleting release:', error)
  }
}

async function refreshCurrentRelease() {
  if (currentRelease.value?.id) {
    await releaseStore.fetchRelease(currentRelease.value.id)
  }
}

function getStatusColor(status) {
  const statusColors = {
    planning: 'blue',
    development: 'orange',
    testing: 'purple',
    release: 'green',
    completed: 'teal',
    cancelled: 'red',
  }
  return statusColors[status?.toLowerCase()] || 'grey'
}
</script>

<style scoped>
.v-list-item--active {
  background-color: rgba(25, 118, 210, 0.1) !important;
  border-left: 4px solid #1976d2;
}

.v-list-item:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
</style>
