<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <h3>Risk Part</h3>
      <v-btn color="primary" @click="openAddDialog">
        <v-icon>mdi-plus</v-icon>
        Add Risk
      </v-btn>
    </div>

    <v-data-table :headers="headers" :items="risks" :loading="loading" class="elevation-1">
      <template v-slot:item.probability="{ item }">
        <v-chip :color="getProbabilityColor(item.probability)" size="small">
          {{ item.probability }}
        </v-chip>
      </template>

      <template v-slot:item.impact="{ item }">
        <v-chip :color="getImpactColor(item.impact)" size="small">
          {{ item.impact }}
        </v-chip>
      </template>

      <template v-slot:item.status="{ item }">
        <v-chip :color="getStatusColor(item.status)" size="small">
          {{ item.status }}
        </v-chip>
      </template>

      <template v-slot:item.description="{ item }">
        <div style="max-width: 200px" class="text-truncate">
          {{ item.description }}
        </div>
      </template>

      <template v-slot:item.migration="{ item }">
        <div style="max-width: 150px" class="text-truncate">
          {{ item.migration }}
        </div>
      </template>

      <template v-slot:item.fallback_strategy="{ item }">
        <div style="max-width: 150px" class="text-truncate">
          {{ item.fallback_strategy }}
        </div>
      </template>

      <template v-slot:item.actions="{ item }">
        <v-btn icon size="small" @click="openEditDialog(item)" class="mr-2">
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
        <v-btn icon size="small" color="error" @click="deleteRisk(item)">
          <v-icon>mdi-delete</v-icon>
        </v-btn>
      </template>
    </v-data-table>

    <!-- Add/Edit Dialog -->
    <RiskDialog
      v-model="dialogOpen"
      :risk="selectedRisk"
      :loading="dialogLoading"
      @save="saveRisk"
    />

    <!-- Confirm Delete Dialog -->
    <v-dialog v-model="deleteDialogOpen" max-width="400px">
      <v-card>
        <v-card-title class="text-h5">Confirm Delete</v-card-title>
        <v-card-text>Are you sure you want to delete this risk?</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="deleteDialogOpen = false">Cancel</v-btn>
          <v-btn color="error" @click="confirmDelete" :loading="deleteLoading">Delete</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import RiskDialog from './RiskDialog.vue'
import { useReleasesStore } from '../stores/releases'

const props = defineProps({
  releaseId: Number,
  risks: Array,
})

const emit = defineEmits(['refresh'])

const releasesStore = useReleasesStore()

const loading = ref(false)
const dialogOpen = ref(false)
const dialogLoading = ref(false)
const deleteDialogOpen = ref(false)
const deleteLoading = ref(false)
const selectedRisk = ref(null)
const riskToDelete = ref(null)

const headers = [
  { title: 'Team', key: 'team', sortable: true },
  { title: 'Type', key: 'type', sortable: true },
  { title: 'Description', key: 'description', sortable: false },
  { title: 'Probability', key: 'probability', sortable: true },
  { title: 'Impact', key: 'impact', sortable: true },
  { title: 'Status', key: 'status', sortable: true },
  { title: 'Migration', key: 'migration', sortable: false },
  { title: 'Fallback Strategy', key: 'fallback_strategy', sortable: false },
  { title: 'Keeper', key: 'keeper', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false, width: '120' },
]

function getProbabilityColor(probability) {
  const colors = {
    Low: 'green',
    Medium: 'orange',
    High: 'red',
  }
  return colors[probability] || 'grey'
}

function getImpactColor(impact) {
  const colors = {
    Low: 'green',
    Medium: 'orange',
    High: 'red',
  }
  return colors[impact] || 'grey'
}

function getStatusColor(status) {
  const colors = {
    Open: 'red',
    'In Progress': 'orange',
    Mitigated: 'blue',
    Closed: 'green',
    Accepted: 'grey',
  }
  return colors[status] || 'grey'
}

function openAddDialog() {
  selectedRisk.value = null
  dialogOpen.value = true
}

function openEditDialog(risk) {
  selectedRisk.value = { ...risk }
  dialogOpen.value = true
}

async function saveRisk(riskData) {
  dialogLoading.value = true
  try {
    if (selectedRisk.value?.id) {
      // Update existing risk
      await updateRisk(selectedRisk.value.id, riskData)
    } else {
      // Create new risk
      await createRisk(riskData)
    }
    dialogOpen.value = false
    emit('refresh')
  } catch (error) {
    console.error('Error saving risk:', error)
    // TODO: Show error message to user
  } finally {
    dialogLoading.value = false
  }
}

async function createRisk(riskData) {
  const response = await fetch(
    `${import.meta.env.VITE_API_BASE_URL}/api/releases/${props.releaseId}/risks`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(riskData),
    }
  )

  if (!response.ok) {
    throw new Error('Failed to create risk')
  }

  return response.json()
}

async function updateRisk(riskId, riskData) {
  const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/risks/${riskId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(riskData),
  })

  if (!response.ok) {
    throw new Error('Failed to update risk')
  }

  return response.json()
}

function deleteRisk(risk) {
  riskToDelete.value = risk
  deleteDialogOpen.value = true
}

async function confirmDelete() {
  deleteLoading.value = true
  try {
    const response = await fetch(
      `${import.meta.env.VITE_API_BASE_URL}/api/risks/${riskToDelete.value.id}`,
      {
        method: 'DELETE',
      }
    )

    if (!response.ok) {
      throw new Error('Failed to delete risk')
    }

    deleteDialogOpen.value = false
    emit('refresh')
  } catch (error) {
    console.error('Error deleting risk:', error)
    // TODO: Show error message to user
  } finally {
    deleteLoading.value = false
  }
}
</script>
