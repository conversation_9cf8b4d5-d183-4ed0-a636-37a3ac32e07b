<template>
  <div class="release-summary">
    <div class="mb-4">
      <h2 class="text-h4 mb-2">Release Summary</h2>
      <p class="text-subtitle-1 text-grey-darken-1">Overview of all releases in the system</p>
    </div>

    <!-- Summary Cards -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="2">
        <v-card class="text-center" color="blue-lighten-5">
          <v-card-text>
            <v-icon size="32" color="blue">mdi-rocket-launch</v-icon>
            <div class="text-h4 font-weight-bold text-blue mt-2">{{ totalReleases }}</div>
            <div class="text-body-2 text-grey-darken-1">Total Releases</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="2">
        <v-card class="text-center" color="green-lighten-5">
          <v-card-text>
            <v-icon size="32" color="green">mdi-check-circle</v-icon>
            <div class="text-h4 font-weight-bold text-green mt-2">{{ completedReleases }}</div>
            <div class="text-body-2 text-grey-darken-1">Completed</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="2">
        <v-card class="text-center" color="orange-lighten-5">
          <v-card-text>
            <v-icon size="32" color="orange">mdi-progress-clock</v-icon>
            <div class="text-h4 font-weight-bold text-orange mt-2">{{ inProgressReleases }}</div>
            <div class="text-body-2 text-grey-darken-1">In Progress</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="2">
        <v-card class="text-center" color="grey-lighten-4">
          <v-card-text>
            <v-icon size="32" color="grey">mdi-clock-outline</v-icon>
            <div class="text-h4 font-weight-bold text-grey-darken-2 mt-2">
              {{ planningReleases }}
            </div>
            <div class="text-body-2 text-grey-darken-1">Planning</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="2">
        <v-card class="text-center" color="red-lighten-5">
          <v-card-text>
            <v-icon size="32" color="red">mdi-alert-circle</v-icon>
            <div class="text-h4 font-weight-bold text-red mt-2">{{ highRiskReleases }}</div>
            <div class="text-body-2 text-grey-darken-1">High Risk</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="2">
        <v-card class="text-center" color="purple-lighten-5">
          <v-card-text>
            <v-icon size="32" color="purple">mdi-feature-search</v-icon>
            <div class="text-h4 font-weight-bold text-purple mt-2">{{ totalFeatures }}</div>
            <div class="text-body-2 text-grey-darken-1">Total Features</div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Release Table -->
    <v-card>
      <v-card-title class="d-flex justify-space-between align-center">
        <span>All Releases</span>
        <v-btn color="primary" @click="$emit('createRelease')">
          <v-icon>mdi-plus</v-icon>
          New Release
        </v-btn>
      </v-card-title>

      <v-data-table
        :headers="headers"
        :items="releases"
        :loading="loading"
        item-value="id"
        class="elevation-0"
        @click:row="(_, { item }) => $emit('selectRelease', item)"
        hover
        :items-per-page="10"
        :items-per-page-options="[5, 10, 25, -1]"
        fixed-header
        height="600px"
      >
        <template v-slot:item.state="{ item }">
          <v-chip :color="getStatusColor(item.state)" size="small" variant="flat">
            {{ item.state || 'No Status' }}
          </v-chip>
        </template>

        <template v-slot:item.risk_state="{ item }">
          <v-chip :color="getRiskColor(item.risk_state)" size="small" variant="outlined">
            {{ item.risk_state || 'Unknown' }}
          </v-chip>
        </template>

        <template v-slot:item.prd_signoff="{ item }">
          {{ formatDate(item.prd_signoff) }}
        </template>

        <template v-slot:item.test_strategy_signoff="{ item }">
          {{ formatDate(item.test_strategy_signoff) }}
        </template>

        <template v-slot:item.release_branch_off="{ item }">
          {{ formatDate(item.release_branch_off) }}
        </template>

        <template v-slot:item.release_code_freeze="{ item }">
          {{ formatDate(item.release_code_freeze) }}
        </template>

        <template v-slot:item.created_at="{ item }">
          {{ formatDateTime(item.created_at) }}
        </template>

        <template v-slot:item.updated_at="{ item }">
          {{ formatDateTime(item.updated_at) }}
        </template>

        <template v-slot:item.prd_link="{ item }">
          <v-btn
            v-if="item.prd_link"
            icon="mdi-link"
            size="small"
            variant="text"
            color="primary"
            @click.stop="openLink(item.prd_link)"
          ></v-btn>
          <span v-else class="text-grey">-</span>
        </template>

        <template v-slot:item.test_strategy_link="{ item }">
          <v-btn
            v-if="item.test_strategy_link"
            icon="mdi-link"
            size="small"
            variant="text"
            color="primary"
            @click.stop="openLink(item.test_strategy_link)"
          ></v-btn>
          <span v-else class="text-grey">-</span>
        </template>

        <template v-slot:item.features_count="{ item }">
          <v-chip size="small" color="info" variant="outlined">
            {{ item.feature_summaries?.length || 0 }}
          </v-chip>
        </template>

        <template v-slot:item.risk="{ item }">
          <div v-if="item.risk" class="text-truncate" style="max-width: 150px">
            <v-tooltip activator="parent" location="top">
              {{ item.risk }}
            </v-tooltip>
            {{ truncateText(item.risk, 20) }}
          </div>
          <span v-else class="text-grey">-</span>
        </template>

        <template v-slot:item.softwares="{ item }">
          <div v-if="item.softwares" class="text-truncate" style="max-width: 120px">
            <v-tooltip activator="parent" location="top">
              {{ item.softwares }}
            </v-tooltip>
            {{ truncateText(item.softwares, 15) }}
          </div>
          <span v-else class="text-grey">-</span>
        </template>

        <template v-slot:item.docs="{ item }">
          <div v-if="item.docs" class="text-truncate" style="max-width: 120px">
            <v-tooltip activator="parent" location="top">
              {{ item.docs }}
            </v-tooltip>
            {{ truncateText(item.docs, 15) }}
          </div>
          <span v-else class="text-grey">-</span>
        </template>

        <template v-slot:item.test_report="{ item }">
          <div v-if="item.test_report" class="text-truncate" style="max-width: 120px">
            <v-tooltip activator="parent" location="top">
              {{ item.test_report }}
            </v-tooltip>
            {{ truncateText(item.test_report, 15) }}
          </div>
          <span v-else class="text-grey">-</span>
        </template>

        <template v-slot:item.actions="{ item }">
          <v-btn icon size="small" variant="text" @click.stop="$emit('editRelease', item)">
            <v-icon>mdi-pencil</v-icon>
          </v-btn>
          <v-btn
            icon
            size="small"
            variant="text"
            color="error"
            @click.stop="$emit('deleteRelease', item)"
          >
            <v-icon>mdi-delete</v-icon>
          </v-btn>
        </template>

        <template v-slot:no-data>
          <div class="text-center pa-8">
            <v-icon size="64" color="grey">mdi-rocket-launch-outline</v-icon>
            <div class="text-h6 mt-4 text-grey">No releases found</div>
            <p class="text-grey">Create your first release to get started</p>
          </div>
        </template>
      </v-data-table>
    </v-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  releases: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['selectRelease', 'createRelease', 'editRelease', 'deleteRelease'])

const headers = [
  { title: 'Name', key: 'name', sortable: true, width: '120px' },
  { title: 'Version', key: 'version', sortable: true, width: '100px' },
  { title: 'Status', key: 'state', sortable: true, width: '120px' },
  { title: 'Risk Level', key: 'risk_state', sortable: true, width: '110px' },
  { title: 'Features', key: 'features_count', sortable: true, width: '80px' },
  { title: 'PRD Signoff', key: 'prd_signoff', sortable: true, width: '120px' },
  { title: 'PRD Link', key: 'prd_link', sortable: false, width: '80px' },
  { title: 'Test Strategy', key: 'test_strategy_signoff', sortable: true, width: '120px' },
  { title: 'Test Link', key: 'test_strategy_link', sortable: false, width: '80px' },
  { title: 'Branch Off', key: 'release_branch_off', sortable: true, width: '120px' },
  { title: 'Code Freeze', key: 'release_code_freeze', sortable: true, width: '120px' },
  { title: 'Risk', key: 'risk', sortable: true, width: '150px' },
  { title: 'Softwares', key: 'softwares', sortable: true, width: '120px' },
  { title: 'Docs', key: 'docs', sortable: true, width: '120px' },
  { title: 'Test Report', key: 'test_report', sortable: true, width: '120px' },
  { title: 'Created', key: 'created_at', sortable: true, width: '130px' },
  { title: 'Updated', key: 'updated_at', sortable: true, width: '130px' },
  { title: 'Actions', key: 'actions', sortable: false, width: '100px' },
]

const totalReleases = computed(() => props.releases.length)

const completedReleases = computed(
  () =>
    props.releases.filter(
      (r) => r.state?.toLowerCase() === 'completed' || r.state?.toLowerCase() === 'release'
    ).length
)

const inProgressReleases = computed(
  () =>
    props.releases.filter((r) =>
      ['development', 'testing', 'staging'].includes(r.state?.toLowerCase())
    ).length
)

const planningReleases = computed(
  () => props.releases.filter((r) => r.state?.toLowerCase() === 'planning' || !r.state).length
)

const highRiskReleases = computed(
  () =>
    props.releases.filter((r) => ['high', 'critical'].includes(r.risk_state?.toLowerCase())).length
)

const totalFeatures = computed(() =>
  props.releases.reduce((total, release) => total + (release.feature_summaries?.length || 0), 0)
)

function getStatusColor(status) {
  const statusColors = {
    planning: 'blue',
    development: 'orange',
    testing: 'purple',
    staging: 'cyan',
    release: 'green',
    completed: 'teal',
    cancelled: 'red',
  }
  return statusColors[status?.toLowerCase()] || 'grey'
}

function formatDate(dateString) {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  } catch {
    return '-'
  }
}

function formatDateTime(dateString) {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return '-'
  }
}

function getRiskColor(riskState) {
  const riskColors = {
    low: 'green',
    medium: 'orange',
    high: 'red',
    critical: 'red-darken-2',
    monitored: 'blue',
    mitigated: 'teal',
  }
  return riskColors[riskState?.toLowerCase()] || 'grey'
}

function truncateText(text, maxLength) {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

function openLink(url) {
  if (url) {
    window.open(url, '_blank')
  }
}
</script>

<style scoped>
.release-summary {
  max-width: 100%;
}

.v-data-table tbody tr {
  cursor: pointer;
}

.v-data-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

.v-data-table {
  min-width: 1800px;
}

.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
</style>
