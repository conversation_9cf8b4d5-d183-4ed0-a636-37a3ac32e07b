<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <h3>Quality</h3>
      <v-btn color="primary" @click="showUploadDialog = true">
        <v-icon>mdi-plus</v-icon>
        Add Quality Image
      </v-btn>
    </div>

    <!-- Image Grid -->
    <v-row>
      <v-col v-for="quality in qualities" :key="quality.id" cols="12" md="6" lg="4">
        <v-card class="quality-card" elevation="0">
          <div class="image-container">
            <!-- 可调整大小的图片容器 -->
            <div
              class="resizable-image-container"
              :ref="(el) => setImageRef(quality.id, el)"
              @click="openImagePreview(quality)"
            >
              <v-img
                :src="quality.image_url"
                :width="getImageWidth(quality.id)"
                :height="getImageHeight(quality.id)"
                cover
                class="cursor-pointer resizable-image"
                :lazy-src="quality.image_url"
              >
                <template v-slot:placeholder>
                  <div class="d-flex align-center justify-center fill-height">
                    <v-progress-circular indeterminate color="grey-lighten-5"></v-progress-circular>
                  </div>
                </template>
                <template v-slot:error>
                  <div class="d-flex align-center justify-center fill-height">
                    <v-icon size="48" color="grey">mdi-image-broken-variant</v-icon>
                  </div>
                </template>
              </v-img>
              <!-- 图片区域内的控制点（悬停时显示） -->
              <div
                class="resize-handle resize-handle-bottom resize-handle-inner"
                @mousedown="startResize($event, quality.id, 's')"
                title="拖拽调整高度"
              ></div>
              <div
                class="resize-handle resize-handle-right resize-handle-inner"
                @mousedown="startResize($event, quality.id, 'e')"
                title="拖拽调整宽度"
              ></div>
            </div>
            <v-overlay :model-value="false" contained class="image-overlay" scrim="rgba(0,0,0,0.3)">
              <v-btn icon="mdi-eye" size="large" color="white" variant="text"></v-btn>
            </v-overlay>
          </div>

          <!-- 主控制点：卡片右下角加粗线条 -->
          <div
            class="resize-corner"
            @mousedown="startResize($event, quality.id, 'se')"
            title="拖拽调整图片大小"
          ></div>

          <!-- 拖拽提示（悬停时显示） -->
          <div class="resize-hint">
            <v-icon size="12" color="grey">mdi-drag</v-icon>
            <span class="text-caption text-grey ml-1">可拖拽调整大小</span>
          </div>
          <!-- 紧凑的标题和操作按钮布局 -->
          <div class="compact-footer pa-2">
            <div class="d-flex align-center justify-space-between">
              <span class="text-subtitle-2 text-truncate flex-grow-1 mr-2">
                {{ quality.title || '无标题' }}
              </span>
              <div class="d-flex align-center">
                <v-btn
                  icon="mdi-pencil"
                  size="x-small"
                  variant="text"
                  @click.stop="editQualityImage(quality)"
                  title="编辑"
                ></v-btn>
                <v-btn
                  icon="mdi-delete"
                  size="x-small"
                  variant="text"
                  color="error"
                  @click.stop="deleteQualityImage(quality)"
                  title="删除"
                ></v-btn>
                <v-btn
                  icon="mdi-download"
                  size="x-small"
                  variant="text"
                  @click.stop="downloadImage(quality)"
                  title="下载"
                ></v-btn>
              </div>
            </div>
          </div>
        </v-card>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <div v-if="qualities.length === 0" class="text-center pa-8">
      <v-icon size="64" color="grey">mdi-image-outline</v-icon>
      <p class="text-grey mt-4">还没有上传质量相关图片</p>
      <p class="text-grey-lighten-1">点击上方按钮添加图片，或直接粘贴图片到此区域</p>
    </div>

    <!-- Upload Dialog -->
    <v-dialog v-model="showUploadDialog" max-width="600px" persistent>
      <v-card>
        <v-card-title>
          <span class="text-h5">{{ editingQuality ? '编辑质量图片' : '添加质量图片' }}</span>
        </v-card-title>
        <v-card-text>
          <v-container>
            <v-row>
              <v-col cols="12">
                <v-text-field
                  v-model="imageTitle"
                  label="图片标题"
                  required
                  :error-messages="titleErrors"
                ></v-text-field>
              </v-col>
              <v-col cols="12">
                <!-- 拖拽上传区域 -->
                <div
                  ref="dropZone"
                  class="upload-area"
                  :class="{ 'drag-over': isDragOver }"
                  @dragover.prevent="isDragOver = true"
                  @dragleave.prevent="isDragOver = false"
                  @drop.prevent="handleDrop"
                  @click="triggerFileInput"
                  @paste="handlePaste"
                  tabindex="0"
                >
                  <div v-if="!selectedFile && !editingQuality" class="upload-content">
                    <v-icon size="48" color="grey">mdi-cloud-upload</v-icon>
                    <p class="mt-2 text-grey">拖拽图片到此处或点击选择文件</p>
                    <p class="text-caption text-grey-lighten-1">
                      支持 JPG, PNG, GIF 格式，最大 10MB
                    </p>
                    <p class="text-caption text-grey-lighten-1">也可以直接 Ctrl+V 粘贴图片</p>
                  </div>

                  <!-- 预览选中的文件 -->
                  <div v-if="selectedFile" class="preview-container">
                    <v-img :src="previewUrl" height="200" contain></v-img>
                    <v-btn
                      icon="mdi-close"
                      size="small"
                      color="error"
                      class="remove-btn"
                      @click.stop="clearSelectedFile"
                    ></v-btn>
                  </div>

                  <!-- 编辑模式下显示当前图片 -->
                  <div v-if="editingQuality && !selectedFile" class="preview-container">
                    <v-img :src="editingQuality.image_url" height="200" contain></v-img>
                    <p class="text-caption text-grey mt-2">点击区域替换图片</p>
                  </div>
                </div>

                <!-- 隐藏的文件输入 -->
                <input
                  ref="fileInput"
                  type="file"
                  accept="image/*"
                  style="display: none"
                  @change="handleFileSelect"
                />
              </v-col>
            </v-row>
          </v-container>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="cancelUpload">取消</v-btn>
          <v-btn color="primary" @click="uploadImage" :loading="uploading" :disabled="!canSave">
            {{ editingQuality ? '保存' : '上传' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Image Preview Dialog with Resizable Image -->
    <v-dialog v-model="showPreviewDialog" max-width="95vw" max-height="95vh">
      <v-card v-if="previewingImage" style="height: 90vh; overflow: hidden">
        <v-card-title class="d-flex justify-space-between align-center pa-2">
          <span>{{ previewingImage.title || '质量图片' }}</span>
          <div class="d-flex align-center gap-2">
            <v-btn
              icon="mdi-restore"
              size="small"
              variant="text"
              @click="resetPreviewImageSize"
              title="重置大小"
            ></v-btn>
            <v-btn icon="mdi-close" size="small" @click="showPreviewDialog = false"></v-btn>
          </div>
        </v-card-title>
        <v-card-text class="pa-2" style="height: calc(100% - 64px); overflow: auto">
          <div class="d-flex justify-center align-center" style="height: 100%">
            <!-- 可调整大小的预览图片 -->
            <div
              class="resizable-preview-container"
              ref="previewImageContainer"
              :style="{
                width: previewImageSize.width + 'px',
                height: previewImageSize.height + 'px',
                position: 'relative',
              }"
            >
              <v-img
                :src="previewingImage.image_url"
                :width="previewImageSize.width"
                :height="previewImageSize.height"
                contain
                class="resizable-preview-image"
              ></v-img>
              <!-- 预览图片的调整大小控制点 -->
              <div
                class="resize-handle resize-handle-bottom-right preview-resize"
                @mousedown="startPreviewResize($event, 'se')"
              ></div>
              <div
                class="resize-handle resize-handle-bottom preview-resize"
                @mousedown="startPreviewResize($event, 's')"
              ></div>
              <div
                class="resize-handle resize-handle-right preview-resize"
                @mousedown="startPreviewResize($event, 'e')"
              ></div>
              <div
                class="resize-handle resize-handle-top preview-resize"
                @mousedown="startPreviewResize($event, 'n')"
              ></div>
              <div
                class="resize-handle resize-handle-left preview-resize"
                @mousedown="startPreviewResize($event, 'w')"
              ></div>
              <div
                class="resize-handle resize-handle-top-left preview-resize"
                @mousedown="startPreviewResize($event, 'nw')"
              ></div>
              <div
                class="resize-handle resize-handle-top-right preview-resize"
                @mousedown="startPreviewResize($event, 'ne')"
              ></div>
              <div
                class="resize-handle resize-handle-bottom-left preview-resize"
                @mousedown="startPreviewResize($event, 'sw')"
              ></div>
            </div>
          </div>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- Delete Confirmation Dialog -->
    <v-dialog v-model="showDeleteDialog" max-width="400px">
      <v-card>
        <v-card-title>确认删除</v-card-title>
        <v-card-text>
          确定要删除图片 "{{ deletingQuality?.title || '无标题' }}" 吗？此操作无法撤销。
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="showDeleteDialog = false">取消</v-btn>
          <v-btn color="error" @click="confirmDelete" :loading="deleting">删除</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Messages -->
    <v-snackbar v-model="showMessage" :color="messageColor" :timeout="3000">
      {{ message }}
    </v-snackbar>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const props = defineProps({
  releaseId: Number,
  qualities: Array,
})

const emit = defineEmits(['refresh'])

// 状态管理
const showUploadDialog = ref(false)
const showPreviewDialog = ref(false)
const showDeleteDialog = ref(false)
const uploading = ref(false)
const deleting = ref(false)

// 图片上传相关
const selectedFile = ref(null)
const previewUrl = ref('')
const imageTitle = ref('')
const titleErrors = ref([])
const isDragOver = ref(false)

// 编辑和删除
const editingQuality = ref(null)
const deletingQuality = ref(null)
const previewingImage = ref(null)

// 消息提示
const showMessage = ref(false)
const message = ref('')
const messageColor = ref('success')

// DOM 引用
const dropZone = ref(null)
const fileInput = ref(null)
const previewImageContainer = ref(null)

// 图片调整大小相关
const imageRefs = ref({})
const imageSizes = ref({})
const isResizing = ref(false)
const resizeData = ref({})

// 预览图片大小
const previewImageSize = ref({ width: 600, height: 400 })
const isPreviewResizing = ref(false)
const previewResizeData = ref({})

// 计算属性
const canSave = computed(() => {
  return imageTitle.value.trim() && (selectedFile.value || editingQuality.value)
})

// 生命周期
onMounted(() => {
  // 监听全局粘贴事件
  document.addEventListener('paste', handleGlobalPaste)
  // 加载保存的图片尺寸
  loadImageSizes()
})

onUnmounted(() => {
  document.removeEventListener('paste', handleGlobalPaste)
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
  // 清理样式
  document.body.style.userSelect = ''
  document.body.style.cursor = ''
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }
})

// 图片调整大小功能
function setImageRef(qualityId, el) {
  if (el) {
    imageRefs.value[qualityId] = el
  }
}

function getImageHeight(qualityId) {
  return imageSizes.value[qualityId]?.height || 200
}

function getImageWidth(qualityId) {
  return imageSizes.value[qualityId]?.width || 300
}

function loadImageSizes() {
  try {
    const savedSizes = localStorage.getItem('quality-image-sizes')
    if (savedSizes) {
      imageSizes.value = JSON.parse(savedSizes)
    }
  } catch (error) {
    console.warn('Failed to load image sizes from localStorage:', error)
  }
}

function saveImageSizes() {
  try {
    localStorage.setItem('quality-image-sizes', JSON.stringify(imageSizes.value))
  } catch (error) {
    console.warn('Failed to save image sizes to localStorage:', error)
  }
}

function initializeImageSize(qualityId) {
  if (!imageSizes.value[qualityId]) {
    imageSizes.value[qualityId] = { width: 300, height: 200 }
  }
}

function startResize(event, qualityId, direction) {
  event.preventDefault()
  event.stopPropagation()

  // 确保图片尺寸已初始化
  initializeImageSize(qualityId)

  isResizing.value = true
  resizeData.value = {
    qualityId,
    direction,
    startX: event.clientX,
    startY: event.clientY,
    startSize: { ...imageSizes.value[qualityId] },
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)

  // 添加防止选择文本的样式
  document.body.style.userSelect = 'none'
  document.body.style.cursor = getResizeCursor(direction)
}

function getResizeCursor(direction) {
  const cursors = {
    n: 'n-resize',
    s: 'n-resize',
    e: 'e-resize',
    w: 'e-resize',
    ne: 'ne-resize',
    nw: 'nw-resize',
    se: 'nw-resize',
    sw: 'ne-resize',
  }
  return cursors[direction] || 'default'
}

// 预览图片调整大小功能

function startPreviewResize(event, direction) {
  event.preventDefault()
  event.stopPropagation()

  isPreviewResizing.value = true
  previewResizeData.value = {
    direction,
    startX: event.clientX,
    startY: event.clientY,
    startSize: { ...previewImageSize.value },
  }

  document.addEventListener('mousemove', handleMouseMove)
  document.addEventListener('mouseup', handleMouseUp)
}

function handleMouseMove(event) {
  // 主页面图片调整大小
  if (isResizing.value && resizeData.value) {
    const { qualityId, direction, startX, startY, startSize } = resizeData.value
    const deltaX = event.clientX - startX
    const deltaY = event.clientY - startY

    let newWidth = startSize.width
    let newHeight = startSize.height

    if (direction.includes('e')) newWidth += deltaX
    if (direction.includes('w')) newWidth -= deltaX
    if (direction.includes('s')) newHeight += deltaY
    if (direction.includes('n')) newHeight -= deltaY

    // 限制最小和最大尺寸
    newWidth = Math.max(150, Math.min(600, newWidth))
    newHeight = Math.max(100, Math.min(500, newHeight))

    imageSizes.value[qualityId] = { width: newWidth, height: newHeight }
  }

  // 预览图片调整大小
  if (isPreviewResizing.value && previewResizeData.value) {
    const { direction, startX, startY, startSize } = previewResizeData.value
    const deltaX = event.clientX - startX
    const deltaY = event.clientY - startY

    let newWidth = startSize.width
    let newHeight = startSize.height

    if (direction.includes('e')) newWidth += deltaX
    if (direction.includes('w')) newWidth -= deltaX
    if (direction.includes('s')) newHeight += deltaY
    if (direction.includes('n')) newHeight -= deltaY

    // 限制最小和最大尺寸
    newWidth = Math.max(200, Math.min(1200, newWidth))
    newHeight = Math.max(150, Math.min(900, newHeight))

    previewImageSize.value = { width: newWidth, height: newHeight }
  }
}

function handleMouseUp() {
  // 主页面图片调整大小结束
  if (isResizing.value) {
    isResizing.value = false
    resizeData.value = {}
    saveImageSizes() // 保存到本地存储
  }

  // 预览图片调整大小结束
  if (isPreviewResizing.value) {
    isPreviewResizing.value = false
    previewResizeData.value = {}
  }

  // 清理样式和事件监听器
  document.body.style.userSelect = ''
  document.body.style.cursor = ''
  document.removeEventListener('mousemove', handleMouseMove)
  document.removeEventListener('mouseup', handleMouseUp)
}

function resetPreviewImageSize() {
  previewImageSize.value = { width: 600, height: 400 }
}

// 文件处理方法
function validateFile(file) {
  if (!file) return false

  if (!file.type.startsWith('image/')) {
    showError('请选择图片文件')
    return false
  }

  if (file.size > 10 * 1024 * 1024) {
    showError('文件大小不能超过 10MB')
    return false
  }

  return true
}

function setSelectedFile(file) {
  if (!validateFile(file)) return

  selectedFile.value = file

  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
  }

  previewUrl.value = URL.createObjectURL(file)
}

function clearSelectedFile() {
  selectedFile.value = null
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value)
    previewUrl.value = ''
  }
}

// 事件处理
function triggerFileInput() {
  fileInput.value?.click()
}

function handleFileSelect(event) {
  const file = event.target.files[0]
  if (file) {
    setSelectedFile(file)
  }
}

function handleDrop(event) {
  isDragOver.value = false
  const files = Array.from(event.dataTransfer.files)
  const imageFile = files.find((file) => file.type.startsWith('image/'))

  if (imageFile) {
    setSelectedFile(imageFile)
  }
}

function handlePaste(event) {
  const items = Array.from(event.clipboardData.items)
  const imageItem = items.find((item) => item.type.startsWith('image/'))

  if (imageItem) {
    const file = imageItem.getAsFile()
    setSelectedFile(file)
  }
}

function handleGlobalPaste(event) {
  // 只在上传对话框打开时处理全局粘贴
  if (showUploadDialog.value) {
    handlePaste(event)
  }
}

// API 调用
async function uploadToServer(file, title) {
  const formData = new FormData()
  formData.append('image', file)
  formData.append('title', title)

  const url = editingQuality.value
    ? `${import.meta.env.VITE_API_BASE_URL}/api/qualities/${editingQuality.value.id}`
    : `${import.meta.env.VITE_API_BASE_URL}/api/releases/${props.releaseId}/qualities`

  const method = editingQuality.value ? 'PUT' : 'POST'

  const response = await fetch(url, {
    method,
    body: formData,
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.error || '上传失败')
  }

  return response.json()
}

async function deleteFromServer(qualityId) {
  const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/qualities/${qualityId}`, {
    method: 'DELETE',
  })

  if (!response.ok) {
    const errorData = await response.json()
    throw new Error(errorData.error || '删除失败')
  }
}

// 主要操作
async function uploadImage() {
  if (!canSave.value) return

  // 验证标题
  titleErrors.value = []
  if (!imageTitle.value.trim()) {
    titleErrors.value.push('请输入图片标题')
    return
  }

  // 编辑模式下如果没有选择新文件，只更新标题
  if (editingQuality.value && !selectedFile.value) {
    await updateQualityTitle()
    return
  }

  // 需要有文件才能上传
  if (!selectedFile.value) {
    showError('请选择要上传的图片')
    return
  }

  uploading.value = true

  try {
    await uploadToServer(selectedFile.value, imageTitle.value.trim())
    showSuccess(editingQuality.value ? '图片已更新' : '图片上传成功')
    cancelUpload()
    emit('refresh')
  } catch (error) {
    console.error('Upload error:', error)
    showError(error.message)
  } finally {
    uploading.value = false
  }
}

async function updateQualityTitle() {
  uploading.value = true

  try {
    const response = await fetch(
      `${import.meta.env.VITE_API_BASE_URL}/api/qualities/${editingQuality.value.id}`,
      {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: imageTitle.value.trim(),
        }),
      }
    )

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(errorData.error || '更新失败')
    }

    showSuccess('标题已更新')
    cancelUpload()
    emit('refresh')
  } catch (error) {
    console.error('Update error:', error)
    showError(error.message)
  } finally {
    uploading.value = false
  }
}

function cancelUpload() {
  showUploadDialog.value = false
  editingQuality.value = null
  clearSelectedFile()
  imageTitle.value = ''
  titleErrors.value = []
}

function editQualityImage(quality) {
  editingQuality.value = quality
  imageTitle.value = quality.title || ''
  showUploadDialog.value = true
}

function deleteQualityImage(quality) {
  deletingQuality.value = quality
  showDeleteDialog.value = true
}

async function confirmDelete() {
  if (!deletingQuality.value) return

  deleting.value = true

  try {
    await deleteFromServer(deletingQuality.value.id)
    showSuccess('图片已删除')
    showDeleteDialog.value = false
    deletingQuality.value = null
    emit('refresh')
  } catch (error) {
    console.error('Delete error:', error)
    showError(error.message)
  } finally {
    deleting.value = false
  }
}

function openImagePreview(quality) {
  previewingImage.value = quality
  showPreviewDialog.value = true
}

function downloadImage(quality) {
  const link = document.createElement('a')
  link.href = quality.image_url
  link.download = `quality-${quality.title || quality.id}`
  document.body.appendChild(link)
  link.click()
  document.body.removeChild(link)
}

// 消息提示
function showSuccess(msg) {
  message.value = msg
  messageColor.value = 'success'
  showMessage.value = true
}

function showError(msg) {
  message.value = msg
  messageColor.value = 'error'
  showMessage.value = true
}
</script>

<style scoped>
/* 可调整大小的图片容器 */
.resizable-image-container {
  position: relative;
  display: block;
  width: 100%; /* 保持响应式宽度 */
  min-width: 150px; /* 最小宽度 */
}

.resizable-image-container:hover .resize-handle {
  opacity: 1;
}

.resizable-image {
  transition: all 0.1s ease;
  width: 100%;
  height: auto;
}

/* 调整大小控制点 */
.resize-handle {
  position: absolute;
  background: #2196f3;
  border: 2px solid white;
  border-radius: 3px;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 10;
}

.resize-handle:hover {
  background: #1976d2;
  transform: scale(1.1);
}

/* 图片内部控制点 */
.resize-handle-inner {
  opacity: 0;
}

/* 右下角线条控制点 */
.resize-corner {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 16px;
  height: 16px;
  cursor: nw-resize;
  z-index: 15;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.resize-corner::before,
.resize-corner::after {
  content: '';
  position: absolute;
  background: #2196f3;
  transition: all 0.2s ease;
}

/* 水平线 */
.resize-corner::before {
  bottom: 2px;
  right: 0;
  width: 12px;
  height: 3px;
}

/* 垂直线 */
.resize-corner::after {
  bottom: 0;
  right: 2px;
  width: 3px;
  height: 12px;
}

.resize-corner:hover {
  opacity: 1;
}

.resize-corner:hover::before,
.resize-corner:hover::after {
  background: #1976d2;
}

.resize-corner:hover::before {
  width: 14px;
  height: 4px;
}

.resize-corner:hover::after {
  width: 4px;
  height: 14px;
}

/* 右下角控制点 */
.resize-handle-bottom-right {
  width: 12px;
  height: 12px;
  bottom: -6px;
  right: -6px;
  cursor: nw-resize;
}

/* 底部控制点 */
.resize-handle-bottom {
  width: 20px;
  height: 8px;
  bottom: -4px;
  left: 50%;
  transform: translateX(-50%);
  cursor: n-resize;
}

/* 右侧控制点 */
.resize-handle-right {
  width: 8px;
  height: 20px;
  right: -4px;
  top: 50%;
  transform: translateY(-50%);
  cursor: e-resize;
}

/* 预览图片专用的调整大小控制点 */
.preview-resize {
  opacity: 0.7;
  background: #ff5722;
}

.preview-resize:hover {
  opacity: 1;
  background: #d84315;
}

/* 预览模式的其他控制点 */
.resize-handle-top {
  width: 20px;
  height: 8px;
  top: -4px;
  left: 50%;
  transform: translateX(-50%);
  cursor: n-resize;
}

.resize-handle-left {
  width: 8px;
  height: 20px;
  left: -4px;
  top: 50%;
  transform: translateY(-50%);
  cursor: e-resize;
}

.resize-handle-top-left {
  width: 12px;
  height: 12px;
  top: -6px;
  left: -6px;
  cursor: nw-resize;
}

.resize-handle-top-right {
  width: 12px;
  height: 12px;
  top: -6px;
  right: -6px;
  cursor: ne-resize;
}

.resize-handle-bottom-left {
  width: 12px;
  height: 12px;
  bottom: -6px;
  left: -6px;
  cursor: ne-resize;
}

/* 可调整大小的预览容器 */
.resizable-preview-container {
  border: 2px dashed rgba(255, 87, 34, 0.3);
  transition: border-color 0.2s ease;
}

.resizable-preview-container:hover {
  border-color: rgba(255, 87, 34, 0.6);
}

.resizable-preview-container:hover .resize-handle {
  opacity: 0.8;
}

/* 主要样式 */
.quality-card {
  height: 100%;
  transition: all 0.2s ease;
  overflow: visible; /* 改为可见，让外侧控制点显示 */
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
}

.quality-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #d0d0d0;
}

.quality-card:hover .resize-corner {
  opacity: 1;
}

.quality-card:hover .resize-hint {
  opacity: 1;
}

.quality-card:hover .resize-handle-inner {
  opacity: 1;
}

.image-overlay {
  opacity: 0;
  transition: opacity 0.3s ease;
}

.image-container:hover .image-overlay {
  opacity: 1;
}

.cursor-pointer {
  cursor: pointer;
}

/* 紧凑的底部布局 */
.compact-footer {
  border-top: 1px solid #e0e0e0;
  background-color: #fafafa;
  min-height: 40px;
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
  position: relative;
}

.compact-footer .v-btn {
  margin: 0 1px;
}

/* 拖拽提示 */
.resize-hint {
  position: absolute;
  top: 8px;
  right: 8px;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 6px;
  border-radius: 12px;
  opacity: 0;
  transition: opacity 0.2s ease;
  z-index: 12;
  display: flex;
  align-items: center;
  backdrop-filter: blur(4px);
  border: 1px solid rgba(0, 0, 0, 0.1);
}

/* 脉动动画 */
@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
    opacity: 0.6;
  }
  50% {
    transform: scale(1.05);
    opacity: 1;
  }
}

/* 确保图片容器不会被外侧控制点影响 */
.image-container {
  position: relative;
  overflow: hidden;
  border-radius: 4px 4px 0 0;
}

/* 上传相关样式 */
.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-area:hover,
.upload-area.drag-over {
  border-color: #2196f3;
  background-color: rgba(33, 150, 243, 0.05);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.preview-container {
  position: relative;
  width: 100%;
}

.remove-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 2;
}
</style>
