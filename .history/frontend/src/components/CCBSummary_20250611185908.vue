<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <h3>CCB Summary</h3>
      <v-btn color="primary" @click="openAddDialog">
        <v-icon>mdi-plus</v-icon>
        Add CCB
      </v-btn>
    </div>

    <v-data-table :headers="headers" :items="ccbSummaries" :loading="loading" class="elevation-1">
      <template v-slot:item.state="{ item }">
        <v-chip :color="getStateColor(item.state)" size="small">
          {{ item.state }}
        </v-chip>
      </template>

      <template v-slot:item.created="{ item }">
        {{ formatDate(item.created) }}
      </template>

      <template v-slot:item.signoff_date="{ item }">
        {{ formatDate(item.signoff_date) }}
      </template>

      <template v-slot:item.description="{ item }">
        <div style="max-width: 200px" class="text-truncate">
          {{ item.description }}
        </div>
      </template>

      <template v-slot:item.actions="{ item }">
        <v-btn icon size="small" @click="openEditDialog(item)" class="mr-2">
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
        <v-btn icon size="small" color="error" @click="deleteCCB(item)">
          <v-icon>mdi-delete</v-icon>
        </v-btn>
      </template>
    </v-data-table>

    <!-- Add/Edit Dialog -->
    <CCBDialog v-model="dialogOpen" :ccb="selectedCCB" :loading="dialogLoading" @save="saveCCB" />

    <!-- Confirm Delete Dialog -->
    <v-dialog v-model="deleteDialogOpen" max-width="400px">
      <v-card>
        <v-card-title class="text-h5">Confirm Delete</v-card-title>
        <v-card-text>Are you sure you want to delete this CCB?</v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn text @click="deleteDialogOpen = false">Cancel</v-btn>
          <v-btn color="error" @click="confirmDelete" :loading="deleteLoading">Delete</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import CCBDialog from './CCBDialog.vue'
import { useReleaseStore } from '../stores/release'

const props = defineProps({
  releaseId: Number,
  ccbSummaries: Array,
})

const emit = defineEmits(['refresh'])

const releaseStore = useReleaseStore()

const loading = ref(false)
const dialogOpen = ref(false)
const dialogLoading = ref(false)
const deleteDialogOpen = ref(false)
const deleteLoading = ref(false)
const selectedCCB = ref(null)
const ccbToDelete = ref(null)

const headers = [
  { title: 'Name', key: 'name', sortable: true },
  { title: 'Creator', key: 'creator', sortable: true },
  { title: 'Description', key: 'description', sortable: false },
  { title: 'State', key: 'state', sortable: true },
  { title: 'Created', key: 'created', sortable: true },
  { title: 'Signoff Date', key: 'signoff_date', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false, width: '120' },
]

function getStateColor(state) {
  const colors = {
    Accept: 'green',
    Reject: 'red',
  }
  return colors[state] || 'grey'
}

function formatDate(dateString) {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString()
}

function openAddDialog() {
  selectedCCB.value = null
  dialogOpen.value = true
}

function openEditDialog(ccb) {
  selectedCCB.value = { ...ccb }
  dialogOpen.value = true
}

async function saveCCB(ccbData) {
  dialogLoading.value = true
  try {
    if (selectedCCB.value?.id) {
      // Update existing CCB
      await updateCCB(selectedCCB.value.id, ccbData)
    } else {
      // Create new CCB
      await createCCB(ccbData)
    }
    dialogOpen.value = false
    emit('refresh')
  } catch (error) {
    console.error('Error saving CCB:', error)
    // TODO: Show error message to user
  } finally {
    dialogLoading.value = false
  }
}

async function createCCB(ccbData) {
  const response = await fetch(
    `${import.meta.env.VITE_API_BASE_URL}/api/releases/${props.releaseId}/ccbs`,
    {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(ccbData),
    }
  )

  if (!response.ok) {
    throw new Error('Failed to create CCB')
  }

  return response.json()
}

async function updateCCB(ccbId, ccbData) {
  const response = await fetch(`${import.meta.env.VITE_API_BASE_URL}/api/ccbs/${ccbId}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(ccbData),
  })

  if (!response.ok) {
    throw new Error('Failed to update CCB')
  }

  return response.json()
}

function deleteCCB(ccb) {
  ccbToDelete.value = ccb
  deleteDialogOpen.value = true
}

async function confirmDelete() {
  deleteLoading.value = true
  try {
    const response = await fetch(
      `${import.meta.env.VITE_API_BASE_URL}/api/ccbs/${ccbToDelete.value.id}`,
      {
        method: 'DELETE',
      }
    )

    if (!response.ok) {
      throw new Error('Failed to delete CCB')
    }

    deleteDialogOpen.value = false
    emit('refresh')
  } catch (error) {
    console.error('Error deleting CCB:', error)
    // TODO: Show error message to user
  } finally {
    deleteLoading.value = false
  }
}
</script>
