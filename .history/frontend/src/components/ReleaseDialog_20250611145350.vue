<template>
  <v-dialog
    :model-value="modelValue"
    @update:model-value="$emit('update:modelValue', $event)"
    max-width="900"
  >
    <v-card>
      <v-card-title>{{ release?.id ? 'Edit Release' : 'Create New Release' }}</v-card-title>

      <v-card-text>
        <v-form ref="form" v-model="valid">
          <!-- Release Name (独立) -->
          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="formData.name"
                label="Release Name"
                :rules="[(v) => !!v || 'Name is required']"
                required
                variant="outlined"
              ></v-text-field>
            </v-col>
          </v-row>

          <!-- MT0 - PRD阶段 -->
          <v-expansion-panels class="mb-4" v-model="expandedPanels" multiple>
            <v-expansion-panel>
              <v-expansion-panel-title>
                <div class="d-flex align-center">
                  <v-chip color="primary" size="small" class="mr-3">MT0</v-chip>
                  <span>PRD 阶段</span>
                </div>
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <v-row>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.prd_signoff"
                      label="PRD Signoff Date"
                      type="date"
                      variant="outlined"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.prd_link"
                      label="PRD Link"
                      type="url"
                      variant="outlined"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-expansion-panel-text>
            </v-expansion-panel>

            <!-- MT1 - 测试策略阶段 -->
            <v-expansion-panel>
              <v-expansion-panel-title>
                <div class="d-flex align-center">
                  <v-chip color="secondary" size="small" class="mr-3">MT1</v-chip>
                  <span>测试策略阶段</span>
                </div>
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <v-row>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.test_strategy_signoff"
                      label="Test Strategy Signoff Date"
                      type="date"
                      variant="outlined"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.test_strategy_link"
                      label="Test Strategy Link"
                      type="url"
                      variant="outlined"
                    ></v-text-field>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12">
                    <v-text-field
                      v-model="formData.release_branch_off"
                      label="Release Branch Off Date"
                      type="date"
                      variant="outlined"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-expansion-panel-text>
            </v-expansion-panel>

            <!-- MT2 - 代码冻结阶段 -->
            <v-expansion-panel>
              <v-expansion-panel-title>
                <div class="d-flex align-center">
                  <v-chip color="warning" size="small" class="mr-3">MT2</v-chip>
                  <span>代码冻结阶段</span>
                </div>
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <v-row>
                  <v-col cols="12">
                    <v-text-field
                      v-model="formData.release_code_freeze"
                      label="Release Code Freeze Date"
                      type="date"
                      variant="outlined"
                    ></v-text-field>
                  </v-col>
                </v-row>
              </v-expansion-panel-text>
            </v-expansion-panel>

            <!-- MT3 - 发布阶段 -->
            <v-expansion-panel>
              <v-expansion-panel-title>
                <div class="d-flex align-center">
                  <v-chip color="success" size="small" class="mr-3">MT3</v-chip>
                  <span>发布阶段</span>
                </div>
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <v-row>
                  <v-col cols="12" md="6">
                    <v-select
                      v-model="formData.state"
                      label="Release State"
                      :items="stateOptions"
                      variant="outlined"
                    ></v-select>
                  </v-col>
                  <v-col cols="12" md="6">
                    <v-select
                      v-model="formData.risk_state"
                      label="Risk State"
                      :items="riskStateOptions"
                      variant="outlined"
                    ></v-select>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.risk_link"
                      label="Risk Link"
                      type="url"
                      variant="outlined"
                    ></v-text-field>
                  </v-col>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.software_download"
                      label="Software Download Link"
                      type="url"
                      variant="outlined"
                    ></v-text-field>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12" md="6">
                    <v-text-field
                      v-model="formData.doc_link"
                      label="Documentation Link"
                      type="url"
                      variant="outlined"
                    ></v-text-field>
                  </v-col>
                </v-row>
                <v-row>
                  <v-col cols="12">
                    <v-textarea
                      v-model="formData.test_report"
                      label="Test Report"
                      rows="3"
                      variant="outlined"
                    ></v-textarea>
                  </v-col>
                </v-row>
              </v-expansion-panel-text>
            </v-expansion-panel>

            <!-- MT4 - 总结阶段 -->
            <v-expansion-panel>
              <v-expansion-panel-title>
                <div class="d-flex align-center">
                  <v-chip color="info" size="small" class="mr-3">MT4</v-chip>
                  <span>总结阶段</span>
                </div>
              </v-expansion-panel-title>
              <v-expansion-panel-text>
                <v-row>
                  <v-col cols="12">
                    <v-textarea
                      v-model="formData.lessons"
                      label="Lessons Learned"
                      rows="4"
                      variant="outlined"
                    ></v-textarea>
                  </v-col>
                </v-row>
              </v-expansion-panel-text>
            </v-expansion-panel>
          </v-expansion-panels>
        </v-form>
      </v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn @click="$emit('update:modelValue', false)">Cancel</v-btn>
        <v-btn color="primary" @click="save" :disabled="!valid">Save</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  release: Object,
})

const emit = defineEmits(['update:modelValue', 'save'])

const form = ref(null)
const valid = ref(false)
const expandedPanels = ref([0, 1, 2, 3, 4]) // 默认展开所有面板

const formData = ref({
  name: '',
  // MT0
  prd_signoff: '',
  prd_link: '',
  // MT1
  test_strategy_signoff: '',
  test_strategy_link: '',
  release_branch_off: '',
  // MT2
  release_code_freeze: '',
  // MT3
  state: '',
  risk_state: '',
  risk_link: '',
  software_download: '',
  doc_link: '',
  test_report: '',
  // MT4
  lessons: '',
})

const stateOptions = ['Planning', 'Development', 'Testing', 'Release', 'Completed', 'Cancelled']

const riskStateOptions = ['Low', 'Medium', 'High', 'Critical']

watch(
  () => props.release,
  (newRelease) => {
    if (newRelease) {
      formData.value = {
        name: newRelease.name || '',
        // MT0
        prd_signoff: formatDateForInput(newRelease.prd_signoff) || '',
        prd_link: newRelease.prd_link || '',
        // MT1
        test_strategy_signoff: formatDateForInput(newRelease.test_strategy_signoff) || '',
        test_strategy_link: newRelease.test_strategy_link || '',
        release_branch_off: formatDateForInput(newRelease.release_branch_off) || '',
        // MT2
        release_code_freeze: formatDateForInput(newRelease.release_code_freeze) || '',
        // MT3
        state: newRelease.state || '',
        risk_state: newRelease.risk_state || '',
        risk_link: newRelease.risk_link || newRelease.risk || '', // 兼容旧字段
        software_download: newRelease.software_download || newRelease.softwares || '', // 兼容旧字段
        doc_link: newRelease.doc_link || newRelease.docs || '', // 兼容旧字段
        test_report: newRelease.test_report || '',
        // MT4
        lessons: newRelease.lessons || '',
      }
    } else {
      resetForm()
    }
  },
  { immediate: true }
)

function resetForm() {
  formData.value = {
    name: '',
    // MT0
    prd_signoff: '',
    prd_link: '',
    // MT1
    test_strategy_signoff: '',
    test_strategy_link: '',
    release_branch_off: '',
    // MT2
    release_code_freeze: '',
    // MT3
    state: '',
    risk_state: '',
    risk_link: '',
    software_download: '',
    doc_link: '',
    test_report: '',
    // MT4
    lessons: '',
  }
}

function formatDateForInput(dateString) {
  if (!dateString) return ''
  return new Date(dateString).toISOString().split('T')[0]
}

async function save() {
  const isValid = await form.value?.validate()
  if (isValid?.valid) {
    const data = { ...formData.value }

    // Convert empty date strings to null
    const dateFields = [
      'prd_signoff',
      'test_strategy_signoff',
      'release_branch_off',
      'release_code_freeze',
    ]
    dateFields.forEach((field) => {
      if (data[field] === '') {
        data[field] = null
      }
    })

    emit('save', data)
  }
}
</script>

<style scoped>
.v-expansion-panel-title {
  font-weight: 500;
}

.v-chip {
  font-weight: 600;
}
</style>
