<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <h3>Risk Part</h3>
      <v-btn color="primary">
        <v-icon>mdi-plus</v-icon>
        Add Risk
      </v-btn>
    </div>
    
    <v-data-table
      :headers="headers"
      :items="risks"
      class="elevation-1"
    >
      <template v-slot:item.probability="{ item }">
        <v-chip :color="getProbabilityColor(item.probability)" size="small">
          {{ item.probability }}
        </v-chip>
      </template>
      <template v-slot:item.impact="{ item }">
        <v-chip :color="getImpactColor(item.impact)" size="small">
          {{ item.impact }}
        </v-chip>
      </template>
    </v-data-table>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

defineProps({
  releaseId: Number,
  risks: Array
})

const headers = [
  { title: 'Team', key: 'team' },
  { title: 'Type', key: 'type' },
  { title: 'Description', key: 'description' },
  { title: 'Probability', key: 'probability' },
  { title: 'Impact', key: 'impact' },
  { title: 'Status', key: 'status' }
]

function getProbabilityColor(probability) {
  const colors = {
    'Low': 'green',
    'Medium': 'orange',
    'High': 'red'
  }
  return colors[probability] || 'grey'
}

function getImpactColor(impact) {
  const colors = {
    'Low': 'green',
    'Medium': 'orange',
    'High': 'red'
  }
  return colors[impact] || 'grey'
}
</script> 