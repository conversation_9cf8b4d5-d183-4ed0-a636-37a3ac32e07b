<template>
  <v-dialog v-model="dialog" max-width="600px" persistent>
    <v-card>
      <v-card-title class="text-h5">{{ isEdit ? 'Edit CCB' : 'Add New CCB' }}</v-card-title>

      <v-card-text>
        <v-container>
          <v-row>
            <v-col cols="12">
              <v-text-field
                v-model="formData.name"
                label="Name"
                required
                :error-messages="errors.name"
              ></v-text-field>
            </v-col>

            <v-col cols="12">
              <v-text-field
                v-model="formData.creator"
                label="Creator"
                required
                :error-messages="errors.creator"
              ></v-text-field>
            </v-col>

            <v-col cols="12">
              <v-textarea
                v-model="formData.description"
                label="Description"
                rows="3"
                :error-messages="errors.description"
              ></v-textarea>
            </v-col>

            <v-col cols="12" sm="6">
              <v-text-field
                v-model="formData.created"
                label="Created Date"
                type="date"
                :error-messages="errors.created"
              ></v-text-field>
            </v-col>

            <v-col cols="12" sm="6">
              <v-text-field
                v-model="formData.signoff_date"
                label="Signoff Date"
                type="date"
                :error-messages="errors.signoff_date"
              ></v-text-field>
            </v-col>

            <v-col cols="12">
              <v-select
                v-model="formData.state"
                label="State"
                :items="stateOptions"
                required
                :error-messages="errors.state"
              ></v-select>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn text @click="closeDialog">Cancel</v-btn>
        <v-btn color="primary" @click="saveData" :loading="loading">
          {{ isEdit ? 'Update' : 'Create' }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  ccb: Object,
  loading: Boolean,
})

const emit = defineEmits(['update:modelValue', 'save'])

const dialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const isEdit = computed(() => !!props.ccb?.id)

const formData = ref({
  name: '',
  creator: '',
  description: '',
  created: '',
  signoff_date: '',
  state: '',
})

const errors = ref({
  name: [],
  creator: [],
  description: [],
  created: [],
  signoff_date: [],
  state: [],
})

const stateOptions = ['Accept', 'Reject']

watch(
  () => props.ccb,
  (newCCB) => {
    if (newCCB) {
      formData.value = {
        name: newCCB.name || '',
        creator: newCCB.creator || '',
        description: newCCB.description || '',
        created: newCCB.created || '',
        signoff_date: newCCB.signoff_date || '',
        state: newCCB.state || '',
      }
    } else {
      resetForm()
    }
  },
  { immediate: true }
)

function resetForm() {
  formData.value = {
    name: '',
    creator: '',
    description: '',
    created: '',
    signoff_date: '',
    state: '',
  }
  errors.value = {
    name: [],
    creator: [],
    description: [],
    created: [],
    signoff_date: [],
    state: [],
  }
}

function validateForm() {
  errors.value = {
    name: [],
    creator: [],
    description: [],
    created: [],
    signoff_date: [],
    state: [],
  }

  let isValid = true

  if (!formData.value.name) {
    errors.value.name.push('Name is required')
    isValid = false
  }

  if (!formData.value.creator) {
    errors.value.creator.push('Creator is required')
    isValid = false
  }

  return isValid
}

function saveData() {
  if (validateForm()) {
    emit('save', { ...formData.value })
  }
}

function closeDialog() {
  dialog.value = false
  resetForm()
}
</script>
