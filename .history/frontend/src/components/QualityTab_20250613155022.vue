<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <h3>Quality</h3>
      <v-btn color="primary" @click="showUploadDialog = true">
        <v-icon>mdi-plus</v-icon>
        Add Quality Image
      </v-btn>
    </div>

    <!-- 图片网格 -->
    <v-row>
      <v-col v-for="quality in qualities" :key="quality.id" :style="getColumnStyle(quality.id)">
        <div class="quality-card">
          <div class="quality-header">
            <div class="quality-title">
              <v-icon size="16" color="primary" class="mr-1">mdi-image</v-icon>
              {{ quality.name }}
            </div>
          </div>

          <!-- 直接使用图片，移除容器 -->
          <div class="image-wrapper" :style="getContainerStyle(quality.id)">
            <img :src="quality.image_url" :alt="quality.name" class="quality-image" />

            <!-- 拖拽控制点 -->
            <div
              class="resize-handle"
              v-if="!isResizing || resizingQualityId !== quality.id"
              @mousedown="startResize($event, quality.id)"
            >
              <v-icon size="14" color="rgba(0,0,0,0.6)">mdi-resize-bottom-right</v-icon>
            </div>

            <!-- 操作按钮组 -->
            <div class="image-actions">
              <v-btn
                icon
                size="small"
                color="primary"
                variant="text"
                @click="downloadImage(quality.image_url)"
                class="action-btn"
              >
                <v-icon size="18">mdi-download</v-icon>
              </v-btn>
              <v-btn
                icon
                size="small"
                color="primary"
                variant="text"
                @click="resetImageSize(quality.id)"
                class="action-btn"
              >
                <v-icon size="18">mdi-image-size-select-actual</v-icon>
              </v-btn>
              <v-btn
                icon
                size="small"
                color="error"
                variant="text"
                @click="deleteQuality(quality.id)"
                class="action-btn"
              >
                <v-icon size="18">mdi-delete</v-icon>
              </v-btn>
            </div>
          </div>
        </div>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <div v-if="qualities.length === 0" class="text-center pa-8">
      <v-icon size="64" color="grey">mdi-image-outline</v-icon>
      <p class="text-grey mt-4">还没有上传质量相关图片</p>
      <p class="text-grey-lighten-1">点击上方按钮添加图片</p>
    </div>

    <!-- Upload Dialog -->
    <v-dialog v-model="showUploadDialog" max-width="600px" persistent>
      <v-card>
        <v-card-title>
          <span class="text-h5">{{
            editingQuality ? 'Edit Quality Image' : 'Add Quality Image'
          }}</span>
        </v-card-title>
        <v-card-text>
          <v-text-field v-model="imageTitle" label="Image Title" required></v-text-field>

          <div v-if="selectedFile" class="preview-img-area">
            <img :src="previewUrl" style="max-width: 100%; max-height: 200px" />
          </div>

          <div class="upload-area" @click="triggerFileInput">
            <v-icon size="48" color="grey">mdi-cloud-upload</v-icon>
            <p v-if="!selectedFile" class="mt-2 text-grey">
              Click to select an image file, or paste a screenshot
            </p>
          </div>

          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            style="display: none"
            @change="handleFileSelect"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="cancelUpload">Cancel</v-btn>
          <v-btn color="primary" @click="uploadImage" :loading="uploading">Upload</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Image Preview Dialog -->
    <v-dialog v-model="showPreviewDialog" max-width="90vw" max-height="90vh">
      <v-card v-if="previewingImage">
        <v-card-title>{{ previewingImage.title || '质量图片' }}</v-card-title>
        <v-card-text class="text-center">
          <img :src="previewingImage.image_url" style="max-width: 100%; max-height: 70vh" />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="showPreviewDialog = false">关闭</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  releaseId: Number,
  qualities: Array,
})

const emit = defineEmits(['refresh'])

// 状态管理
const showUploadDialog = ref(false)
const showPreviewDialog = ref(false)
const uploading = ref(false)
const selectedFile = ref(null)
const imageTitle = ref('')
const editingQuality = ref(null)
const previewingImage = ref(null)
const fileInput = ref(null)
const previewUrl = ref('')

// 容器尺寸管理
const containerSizes = ref({}) // 存储每个图片容器的自定义尺寸
const originalImageSizes = ref({}) // 存储图片的原始尺寸
const isResizing = ref(false)
const resizingQualityId = ref(null)
const resizeStartData = ref({})

// 最小容器尺寸（保证操作栏正常显示）
const MIN_CONTAINER_WIDTH = 200
const MIN_CONTAINER_HEIGHT = 120

// 容器相关函数
function getContainerSize(qualityId) {
  // 如果有自定义尺寸，使用自定义尺寸
  if (containerSizes.value[qualityId]) {
    return containerSizes.value[qualityId]
  }

  // 否则使用原始图片尺寸，但不小于最小尺寸
  const originalSize = originalImageSizes.value[qualityId]
  if (originalSize) {
    return {
      width: Math.max(MIN_CONTAINER_WIDTH, originalSize.width),
      height: Math.max(MIN_CONTAINER_HEIGHT, originalSize.height),
    }
  }

  // 默认尺寸
  return {
    width: MIN_CONTAINER_WIDTH,
    height: MIN_CONTAINER_HEIGHT,
  }
}

function getContainerStyle(qualityId) {
  const size = getContainerSize(qualityId)
  return {
    width: size.width + 'px',
    height: size.height + 'px',
    minWidth: MIN_CONTAINER_WIDTH + 'px',
    minHeight: MIN_CONTAINER_HEIGHT + 'px',
  }
}

function getColumnStyle(qualityId) {
  const size = getContainerSize(qualityId)
  const cardWidth = size.width + 32 // 加上卡片的padding和边距

  return {
    flex: 'none',
    width: cardWidth + 'px',
    maxWidth: '100%',
  }
}

function onImageLoad(event, qualityId) {
  const img = event.target
  originalImageSizes.value[qualityId] = {
    width: img.naturalWidth,
    height: img.naturalHeight,
  }

  // 如果还没有自定义尺寸，使用原始尺寸（但不小于最小尺寸）
  if (!containerSizes.value[qualityId]) {
    containerSizes.value[qualityId] = {
      width: Math.max(MIN_CONTAINER_WIDTH, img.naturalWidth),
      height: Math.max(MIN_CONTAINER_HEIGHT, img.naturalHeight),
    }
    saveContainerSizes()
  }
}

// 拖拽调整大小功能
function startResize(event, qualityId) {
  event.preventDefault()
  event.stopPropagation()

  isResizing.value = true
  resizingQualityId.value = qualityId
  resizeStartData.value = {
    startX: event.clientX,
    startY: event.clientY,
    startSize: { ...getContainerSize(qualityId) },
  }

  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)
  document.body.style.cursor = 'nw-resize'
  document.body.style.userSelect = 'none'

  // 添加拖拽时的样式类
  const wrapper = event.currentTarget.closest('.image-wrapper')
  if (wrapper) {
    wrapper.classList.add('resizing')
  }
}

function handleResizeMove(event) {
  if (!isResizing.value || !resizingQualityId.value) return

  const { startX, startY, startSize } = resizeStartData.value
  const deltaX = event.clientX - startX
  const deltaY = event.clientY - startY

  const newWidth = Math.max(MIN_CONTAINER_WIDTH, startSize.width + deltaX)
  const newHeight = Math.max(MIN_CONTAINER_HEIGHT, startSize.height + deltaY)

  containerSizes.value[resizingQualityId.value] = {
    width: newWidth,
    height: newHeight,
  }
}

function handleResizeEnd() {
  if (!isResizing.value) return

  isResizing.value = false
  resizingQualityId.value = null
  resizeStartData.value = null

  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''

  // 移除拖拽时的样式类
  const wrapper = document.querySelector('.image-wrapper.resizing')
  if (wrapper) {
    wrapper.classList.remove('resizing')
  }

  saveContainerSizes()
}

function loadContainerSizes() {
  try {
    const savedSizes = localStorage.getItem('quality-container-sizes')
    if (savedSizes) {
      containerSizes.value = JSON.parse(savedSizes)
    }
  } catch (error) {
    console.warn('Failed to load container sizes from localStorage:', error)
  }
}

function saveContainerSizes() {
  try {
    localStorage.setItem('quality-container-sizes', JSON.stringify(containerSizes.value))
  } catch (error) {
    console.warn('Failed to save container sizes to localStorage:', error)
  }
}

// 监听qualities变化
watch(
  () => props.qualities,
  (newQualities) => {
    if (newQualities && newQualities.length > 0) {
      for (const quality of newQualities) {
        if (!containerSizes.value[quality.id]) {
          // 初始化时使用默认尺寸，等图片加载后会更新为原始尺寸
          containerSizes.value[quality.id] = {
            width: MIN_CONTAINER_WIDTH,
            height: MIN_CONTAINER_HEIGHT,
          }
        }
      }
    }
  },
  { immediate: true }
)

// 基本函数
function openImagePreview(quality) {
  previewingImage.value = quality
  showPreviewDialog.value = true
}

function editQualityImage(quality) {
  editingQuality.value = quality
  imageTitle.value = quality.title || ''
  showUploadDialog.value = true
}

function deleteQualityImage(quality) {
  if (confirm('确定要删除这张图片吗？')) {
    // 删除逻辑
    console.log('删除图片:', quality.id)
  }
}

function triggerFileInput() {
  fileInput.value?.click()
}

function handleFileSelect(event) {
  const file = event.target.files[0]
  if (file) {
    selectedFile.value = file
    console.log('选择文件:', file.name)
  }
}

function cancelUpload() {
  showUploadDialog.value = false
  imageTitle.value = ''
  selectedFile.value = null
  editingQuality.value = null
}

function uploadImage() {
  console.log('上传图片')
  uploading.value = true

  setTimeout(() => {
    uploading.value = false
    cancelUpload()
    emit('refresh')
  }, 2000)
}

// 重置图片尺寸到原始大小
function resetImageSize(qualityId) {
  const quality = props.qualities.find((q) => q.id === qualityId)
  if (!quality) return

  // 获取图片原始尺寸
  const img = new Image()
  img.onload = () => {
    const originalSize = {
      width: img.width,
      height: img.height,
    }

    // 保存原始尺寸
    containerSizes.value[qualityId] = originalSize
    saveContainerSizes()
  }
  img.src = quality.image_url
}

// 粘贴上传图片功能
function handlePaste(event) {
  const items = event.clipboardData && event.clipboardData.items
  if (!items) return
  for (let i = 0; i < items.length; i++) {
    if (items[i].type.indexOf('image') !== -1) {
      const file = items[i].getAsFile()
      if (file) {
        selectedFile.value = file
        imageTitle.value = file.name || '粘贴图片'
        // 可选：自动上传
        // uploadImage()
      }
    }
  }
}

watch(showUploadDialog, (val) => {
  if (val) {
    window.addEventListener('paste', handlePaste)
  } else {
    window.removeEventListener('paste', handlePaste)
  }
})

watch(selectedFile, (file) => {
  if (file) {
    previewUrl.value = URL.createObjectURL(file)
  } else {
    previewUrl.value = ''
  }
})

onMounted(() => {
  console.log('QualityTab mounted, qualities:', props.qualities)
  loadContainerSizes()
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
})
</script>

<style scoped>
.quality-card {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.quality-header {
  padding: 8px 12px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #eee;
}

.quality-title {
  font-size: 14px;
  font-weight: 500;
  color: #333;
  display: flex;
  align-items: center;
}

.image-wrapper {
  position: relative;
  width: 100%;
  height: 300px;
  background: #f5f5f5;
  overflow: hidden;
}

.quality-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: all 0.2s ease;
}

/* 拖拽控制点 */
.resize-handle {
  position: absolute;
  bottom: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  background: rgba(255, 255, 255, 0.95);
  border: 2px solid rgba(0, 0, 0, 0.3);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: nw-resize;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 15;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
}

.image-wrapper:hover .resize-handle {
  opacity: 1;
}

/* 图片操作按钮组 */
.image-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: all 0.2s ease;
  background: rgba(255, 255, 255, 0.9);
  padding: 4px;
  border-radius: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-wrapper:hover .image-actions {
  opacity: 1;
}

.action-btn {
  opacity: 0.8;
  transition: all 0.2s ease;
}

.action-btn:hover {
  opacity: 1;
  transform: scale(1.1);
}

/* 拖拽时的样式 */
.resizing {
  cursor: nw-resize;
  user-select: none;
}

.resizing .quality-image {
  pointer-events: none;
}

/* 紧凑的底部布局 */
.compact-footer {
  border-top: 1px solid #e0e0e0;
  background-color: #fafafa;
  min-height: 36px;
}

/* 操作按钮区域更窄 */
.action-buttons {
  display: flex;
  gap: 2px;
  min-width: 60px;
}

.action-buttons .v-btn {
  min-width: 28px !important;
  width: 28px;
  height: 28px;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.upload-area:hover {
  border-color: #2196f3;
}

.preview-img-area {
  margin-bottom: 20px;
  text-align: center;
}
</style>
