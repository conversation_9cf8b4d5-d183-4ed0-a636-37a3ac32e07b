<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <h3>Quality</h3>
      <v-btn color="primary">
        <v-icon>mdi-plus</v-icon>
        Add Quality Image
      </v-btn>
    </div>
    
    <v-row>
      <v-col v-for="quality in qualities" :key="quality.id" cols="12" md="6" lg="4">
        <v-card>
          <v-img :src="quality.image_url" height="200" cover></v-img>
          <v-card-title>{{ quality.title }}</v-card-title>
        </v-card>
      </v-col>
    </v-row>
    
    <div v-if="qualities.length === 0" class="text-center pa-8">
      <v-icon size="64" color="grey">mdi-image-outline</v-icon>
      <p class="text-grey mt-4">No quality images uploaded yet</p>
    </div>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

defineProps({
  releaseId: Number,
  qualities: Array
})
</script> 