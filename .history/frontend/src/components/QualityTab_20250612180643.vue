<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <h3>Quality</h3>
      <v-btn color="primary" @click="showUploadDialog = true">
        <v-icon>mdi-plus</v-icon>
        Add Quality Image
      </v-btn>
    </div>

    <!-- 图片网格 -->
    <v-row>
      <v-col v-for="quality in qualities" :key="quality.id" cols="12" md="6" lg="4">
        <v-card class="quality-card" elevation="2">
          <!-- 图片尺寸选择器 -->
          <div class="size-selector-bar">
            <v-btn-group variant="outlined" size="x-small" density="compact">
              <v-btn
                @click="setImageSize(quality.id, 25)"
                :variant="getImageSize(quality.id) === 25 ? 'flat' : 'outlined'"
                :color="getImageSize(quality.id) === 25 ? 'primary' : ''"
                >25%</v-btn
              >
              <v-btn
                @click="setImageSize(quality.id, 50)"
                :variant="getImageSize(quality.id) === 50 ? 'flat' : 'outlined'"
                :color="getImageSize(quality.id) === 50 ? 'primary' : ''"
                >50%</v-btn
              >
              <v-btn
                @click="setImageSize(quality.id, 75)"
                :variant="getImageSize(quality.id) === 75 ? 'flat' : 'outlined'"
                :color="getImageSize(quality.id) === 75 ? 'primary' : ''"
                >75%</v-btn
              >
              <v-btn
                @click="setImageSize(quality.id, 100)"
                :variant="getImageSize(quality.id) === 100 ? 'flat' : 'outlined'"
                :color="getImageSize(quality.id) === 100 ? 'primary' : ''"
                >100%</v-btn
              >
              <v-btn
                @click="setImageSize(quality.id, 'original')"
                :variant="getImageSize(quality.id) === 'original' ? 'flat' : 'outlined'"
                :color="getImageSize(quality.id) === 'original' ? 'primary' : ''"
                >原始</v-btn
              >
            </v-btn-group>
          </div>

          <!-- 图片显示区域 -->
          <div class="image-container" :style="getContainerStyle(quality.id)">
            <img
              :src="quality.image_url"
              :style="getImageStyle(quality.id)"
              class="quality-image"
              @click="openImagePreview(quality)"
              @load="onImageLoad($event, quality.id)"
            />
          </div>

          <!-- 紧凑的标题和操作按钮 -->
          <v-card-text class="compact-footer pa-2">
            <div class="d-flex align-center">
              <!-- 标题占大部分空间 -->
              <span class="text-subtitle-2 text-truncate flex-grow-1 mr-2">
                {{ quality.title || '无标题' }}
              </span>
              <!-- 操作按钮区域更窄 -->
              <div class="action-buttons">
                <v-btn
                  icon="mdi-pencil"
                  size="x-small"
                  variant="text"
                  @click="editQualityImage(quality)"
                  density="compact"
                ></v-btn>
                <v-btn
                  icon="mdi-delete"
                  size="x-small"
                  variant="text"
                  color="error"
                  @click="deleteQualityImage(quality)"
                  density="compact"
                ></v-btn>
              </div>
            </div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Empty State -->
    <div v-if="qualities.length === 0" class="text-center pa-8">
      <v-icon size="64" color="grey">mdi-image-outline</v-icon>
      <p class="text-grey mt-4">还没有上传质量相关图片</p>
      <p class="text-grey-lighten-1">点击上方按钮添加图片</p>
    </div>

    <!-- Upload Dialog -->
    <v-dialog v-model="showUploadDialog" max-width="600px" persistent>
      <v-card>
        <v-card-title>
          <span class="text-h5">{{ editingQuality ? '编辑质量图片' : '添加质量图片' }}</span>
        </v-card-title>
        <v-card-text>
          <v-text-field v-model="imageTitle" label="图片标题" required></v-text-field>

          <div class="upload-area" @click="triggerFileInput">
            <v-icon size="48" color="grey">mdi-cloud-upload</v-icon>
            <p class="mt-2 text-grey">点击选择图片文件</p>
          </div>

          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            style="display: none"
            @change="handleFileSelect"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="cancelUpload">取消</v-btn>
          <v-btn color="primary" @click="uploadImage" :loading="uploading">上传</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Image Preview Dialog -->
    <v-dialog v-model="showPreviewDialog" max-width="90vw" max-height="90vh">
      <v-card v-if="previewingImage">
        <v-card-title>{{ previewingImage.title || '质量图片' }}</v-card-title>
        <v-card-text class="text-center">
          <img :src="previewingImage.image_url" style="max-width: 100%; max-height: 70vh" />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="showPreviewDialog = false">关闭</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'

const props = defineProps({
  releaseId: Number,
  qualities: Array,
})

const emit = defineEmits(['refresh'])

// 状态管理
const showUploadDialog = ref(false)
const showPreviewDialog = ref(false)
const uploading = ref(false)
const selectedFile = ref(null)
const imageTitle = ref('')
const editingQuality = ref(null)
const previewingImage = ref(null)
const fileInput = ref(null)

// 图片尺寸管理
const imageSizes = ref({}) // 存储每个图片的尺寸百分比
const originalImageSizes = ref({}) // 存储图片的原始尺寸

// 图片尺寸相关函数
function getImageSize(qualityId) {
  return imageSizes.value[qualityId] || 100 // 默认100%
}

function setImageSize(qualityId, size) {
  imageSizes.value[qualityId] = size
  saveImageSizes()
}

function getImageStyle(qualityId) {
  const size = getImageSize(qualityId)
  const originalSize = originalImageSizes.value[qualityId]

  if (size === 'original' && originalSize) {
    return {
      width: originalSize.width + 'px',
      height: originalSize.height + 'px',
      maxWidth: '100%',
      cursor: 'pointer',
    }
  } else if (typeof size === 'number') {
    return {
      width: size + '%',
      height: 'auto',
      maxWidth: '100%',
      cursor: 'pointer',
    }
  }

  return {
    width: '100%',
    height: 'auto',
    cursor: 'pointer',
  }
}

function getContainerStyle(qualityId) {
  const size = getImageSize(qualityId)
  const originalSize = originalImageSizes.value[qualityId]

  if (size === 'original' && originalSize) {
    // 原始尺寸：容器适应图片实际大小
    return {
      width: Math.min(originalSize.width + 16, 400) + 'px', // 加上padding，但限制最大宽度
      height: 'auto',
      minHeight: Math.min(originalSize.height + 16, 300) + 'px', // 限制最大高度
    }
  } else if (typeof size === 'number') {
    // 百分比：容器宽度跟随，但设置合理的最小和最大值
    const containerWidth = Math.max(120, Math.min(400, size * 3)) // 根据百分比调整容器宽度
    return {
      width: containerWidth + 'px',
      height: 'auto',
      minHeight: '80px',
    }
  }

  // 默认样式
  return {
    width: '100%',
    height: 'auto',
    minHeight: '100px',
  }
}

function onImageLoad(event, qualityId) {
  const img = event.target
  originalImageSizes.value[qualityId] = {
    width: img.naturalWidth,
    height: img.naturalHeight,
  }
}

function loadImageSizes() {
  try {
    const savedSizes = localStorage.getItem('quality-image-sizes')
    if (savedSizes) {
      imageSizes.value = JSON.parse(savedSizes)
    }
  } catch (error) {
    console.warn('Failed to load image sizes from localStorage:', error)
  }
}

function saveImageSizes() {
  try {
    localStorage.setItem('quality-image-sizes', JSON.stringify(imageSizes.value))
  } catch (error) {
    console.warn('Failed to save image sizes to localStorage:', error)
  }
}

// 监听qualities变化，初始化图片尺寸
watch(
  () => props.qualities,
  (newQualities) => {
    if (newQualities && newQualities.length > 0) {
      for (const quality of newQualities) {
        if (!imageSizes.value[quality.id]) {
          imageSizes.value[quality.id] = 100 // 默认100%
        }
      }
    }
  },
  { immediate: true }
)

// 基本函数
function openImagePreview(quality) {
  previewingImage.value = quality
  showPreviewDialog.value = true
}

function editQualityImage(quality) {
  editingQuality.value = quality
  imageTitle.value = quality.title || ''
  showUploadDialog.value = true
}

function deleteQualityImage(quality) {
  if (confirm('确定要删除这张图片吗？')) {
    // 删除逻辑
    console.log('删除图片:', quality.id)
  }
}

function triggerFileInput() {
  fileInput.value?.click()
}

function handleFileSelect(event) {
  const file = event.target.files[0]
  if (file) {
    selectedFile.value = file
    console.log('选择文件:', file.name)
  }
}

function cancelUpload() {
  showUploadDialog.value = false
  imageTitle.value = ''
  selectedFile.value = null
  editingQuality.value = null
}

function uploadImage() {
  console.log('上传图片')
  uploading.value = true

  setTimeout(() => {
    uploading.value = false
    cancelUpload()
    emit('refresh')
  }, 2000)
}

onMounted(() => {
  console.log('QualityTab mounted, qualities:', props.qualities)
  loadImageSizes()
})
</script>

<style scoped>
.quality-card {
  transition: transform 0.2s ease;
  position: relative;
}

.quality-card:hover {
  transform: translateY(-2px);
}

/* 尺寸选择器栏 */
.size-selector-bar {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 4px;
  padding: 4px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.quality-card:hover .size-selector-bar {
  opacity: 1;
}

/* 图片容器 */
.image-container {
  padding: 8px;
  text-align: center;
  background: #f5f5f5;
  min-height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease; /* 平滑的尺寸变化动画 */
  margin: 0 auto; /* 居中显示 */
}

.quality-image {
  border-radius: 4px;
  transition: transform 0.2s ease;
  max-width: 100%;
  height: auto;
}

.quality-image:hover {
  transform: scale(1.02);
}

/* 紧凑的底部布局 */
.compact-footer {
  border-top: 1px solid #e0e0e0;
  background-color: #fafafa;
  min-height: 36px;
}

/* 操作按钮区域更窄 */
.action-buttons {
  display: flex;
  gap: 2px;
  min-width: 60px; /* 固定最小宽度，让按钮区域更紧凑 */
}

.action-buttons .v-btn {
  min-width: 28px !important;
  width: 28px;
  height: 28px;
}

.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.upload-area:hover {
  border-color: #2196f3;
}
</style>
