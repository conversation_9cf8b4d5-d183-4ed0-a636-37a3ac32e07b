<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <h3>CCB Summary</h3>
      <v-btn color="primary">
        <v-icon>mdi-plus</v-icon>
        Add CCB
      </v-btn>
    </div>
    
    <v-data-table
      :headers="headers"
      :items="ccbSummaries"
      class="elevation-1"
    >
    </v-data-table>
  </div>
</template>

<script setup>
import { defineProps } from 'vue'

defineProps({
  releaseId: Number,
  ccbSummaries: Array
})

const headers = [
  { title: 'Name', key: 'name' },
  { title: 'Creator', key: 'creator' },
  { title: 'State', key: 'state' },
  { title: 'Created', key: 'created' }
]
</script>