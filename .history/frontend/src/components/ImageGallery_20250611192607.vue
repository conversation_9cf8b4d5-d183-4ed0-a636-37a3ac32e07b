<template>
  <v-card class="pa-4">
    <v-card-title class="d-flex justify-space-between align-center">
      <span>图片管理</span>
      <div class="d-flex align-center">
        <v-chip
          :color="storageUsage > 80 ? 'error' : storageUsage > 60 ? 'warning' : 'success'"
          variant="outlined"
          size="small"
          class="mr-2"
        >
          存储: {{ formatFileSize(currentStorageSize) }} / 5MB
        </v-chip>
        <v-btn
          color="primary"
          variant="outlined"
          size="small"
          @click="clearAllImages"
          :disabled="images.length === 0"
        >
          清空所有
        </v-btn>
      </div>
    </v-card-title>

    <!-- 文件上传区域 -->
    <v-card class="mb-4 pa-4" variant="outlined" style="border: 2px dashed #ccc">
      <div class="text-center" @drop="handleDrop" @dragover.prevent @dragenter.prevent>
        <v-icon size="48" color="grey">mdi-cloud-upload</v-icon>
        <p class="mt-2 mb-2">拖拽图片到此处或点击选择</p>
        <v-file-input
          v-model="selectedFiles"
          label="选择图片文件"
          accept="image/*"
          multiple
          variant="outlined"
          density="compact"
          hide-details
          @change="handleFileSelect"
        ></v-file-input>
        <p class="text-caption text-grey mt-2">支持 JPG, PNG, GIF, WebP 格式，总大小限制 5MB</p>
      </div>
    </v-card>

    <!-- 工具栏 -->
    <div class="d-flex justify-space-between align-center mb-4">
      <div class="d-flex align-center">
        <v-select
          v-model="gridSize"
          :items="gridSizeOptions"
          label="网格大小"
          variant="outlined"
          density="compact"
          style="width: 150px"
          hide-details
        ></v-select>
        <v-spacer></v-spacer>
        <v-switch
          v-model="enableResize"
          label="允许调整大小"
          color="primary"
          hide-details
          class="ml-4"
        ></v-switch>
      </div>
      <div>
        <v-btn color="success" variant="outlined" size="small" @click="selectAll" class="mr-2">
          全选
        </v-btn>
        <v-btn
          color="error"
          variant="outlined"
          size="small"
          @click="deleteSelected"
          :disabled="selectedImages.length === 0"
        >
          删除选中 ({{ selectedImages.length }})
        </v-btn>
      </div>
    </div>

    <!-- 图片网格 -->
    <div
      class="image-grid"
      :style="{
        gridTemplateColumns: `repeat(auto-fill, minmax(${gridSize}px, 1fr))`,
        gap: '16px',
      }"
    >
      <div
        v-for="(image, index) in images"
        :key="image.id"
        class="image-item"
        :class="{
          selected: selectedImages.includes(image.id),
          resizable: enableResize,
        }"
        @click="toggleSelection(image.id)"
      >
        <!-- 选择框 -->
        <v-checkbox
          v-model="selectedImages"
          :value="image.id"
          class="image-checkbox"
          hide-details
          @click.stop
        ></v-checkbox>

        <!-- 图片容器 -->
        <div
          class="image-container"
          :style="
            enableResize
              ? {
                  width: image.width + 'px',
                  height: image.height + 'px',
                }
              : {}
          "
        >
          <img
            :src="image.src"
            :alt="image.name"
            class="image-display"
            @load="onImageLoad(index)"
          />

          <!-- 调整大小手柄 -->
          <div
            v-if="enableResize"
            class="resize-handle"
            @mousedown="startResize($event, index)"
          ></div>
        </div>

        <!-- 图片信息 -->
        <div class="image-info">
          <v-tooltip bottom>
            <template v-slot:activator="{ props }">
              <p v-bind="props" class="image-name">{{ image.name }}</p>
            </template>
            <span>{{ image.name }}</span>
          </v-tooltip>
          <p class="image-size">{{ formatFileSize(image.size) }}</p>
        </div>

        <!-- 操作按钮 -->
        <div class="image-actions">
          <v-btn icon size="small" color="primary" variant="text" @click.stop="previewImage(image)">
            <v-icon>mdi-eye</v-icon>
          </v-btn>
          <v-btn icon size="small" color="error" variant="text" @click.stop="deleteImage(image.id)">
            <v-icon>mdi-delete</v-icon>
          </v-btn>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-if="images.length === 0" class="text-center py-8">
      <v-icon size="64" color="grey">mdi-image-multiple</v-icon>
      <p class="text-h6 mt-2 text-grey">暂无图片</p>
      <p class="text-grey">上传一些图片开始使用</p>
    </div>

    <!-- 图片预览对话框 -->
    <v-dialog v-model="previewDialog" max-width="90vw">
      <v-card v-if="previewImage">
        <v-card-title class="d-flex justify-space-between align-center">
          <span>{{ previewImage.name }}</span>
          <v-btn icon @click="previewDialog = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text class="pa-0">
          <img
            :src="previewImage.src"
            :alt="previewImage.name"
            style="width: 100%; height: auto; max-height: 80vh; object-fit: contain"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <p class="text-caption">
            大小: {{ formatFileSize(previewImage.size) }} | 尺寸: {{ previewImage.naturalWidth }}x{{
              previewImage.naturalHeight
            }}
          </p>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<script>
export default {
  name: 'ImageGallery',
  data() {
    return {
      images: [],
      selectedFiles: [],
      selectedImages: [],
      currentStorageSize: 0,
      maxStorageSize: 5 * 1024 * 1024, // 5MB
      gridSize: 200,
      gridSizeOptions: [
        { title: '小 (150px)', value: 150 },
        { title: '中 (200px)', value: 200 },
        { title: '大 (250px)', value: 250 },
        { title: '特大 (300px)', value: 300 },
      ],
      enableResize: false,
      previewDialog: false,
      previewImage: null,
      resizing: {
        active: false,
        imageIndex: -1,
        startX: 0,
        startY: 0,
        startWidth: 0,
        startHeight: 0,
      },
    }
  },
  computed: {
    storageUsage() {
      return (this.currentStorageSize / this.maxStorageSize) * 100
    },
  },
  mounted() {
    this.loadFromLocalStorage()
    document.addEventListener('mousemove', this.handleResize)
    document.addEventListener('mouseup', this.stopResize)
  },
  beforeUnmount() {
    document.removeEventListener('mousemove', this.handleResize)
    document.removeEventListener('mouseup', this.stopResize)
  },
  methods: {
    handleFileSelect(event) {
      const files = event.target ? event.target.files : event
      this.processFiles(Array.from(files))
    },

    handleDrop(event) {
      event.preventDefault()
      const files = Array.from(event.dataTransfer.files)
      this.processFiles(files.filter((file) => file.type.startsWith('image/')))
    },

    processFiles(files) {
      const validFiles = files.filter((file) => {
        if (!file.type.startsWith('image/')) {
          this.$emit('message', { type: 'error', text: `${file.name} 不是有效的图片文件` })
          return false
        }
        return true
      })

      let totalSize = this.currentStorageSize
      const filesToProcess = []

      for (const file of validFiles) {
        if (totalSize + file.size > this.maxStorageSize) {
          this.$emit('message', {
            type: 'error',
            text: '存储空间不足，无法添加更多图片',
          })
          break
        }
        filesToProcess.push(file)
        totalSize += file.size
      }

      filesToProcess.forEach((file) => {
        const reader = new FileReader()
        reader.onload = (e) => {
          const image = {
            id: Date.now() + Math.random(),
            name: file.name,
            src: e.target.result,
            size: file.size,
            width: this.gridSize,
            height: this.gridSize,
            naturalWidth: 0,
            naturalHeight: 0,
            timestamp: new Date().toISOString(),
          }
          this.images.push(image)
          this.currentStorageSize += file.size
          this.saveToLocalStorage()
        }
        reader.readAsDataURL(file)
      })

      // 清空文件输入
      this.selectedFiles = []
    },

    onImageLoad(index) {
      const img = new Image()
      img.onload = () => {
        this.images[index].naturalWidth = img.naturalWidth
        this.images[index].naturalHeight = img.naturalHeight

        // 如果启用调整大小，根据原始比例设置初始大小
        if (this.enableResize) {
          const aspectRatio = img.naturalWidth / img.naturalHeight
          if (aspectRatio > 1) {
            this.images[index].width = this.gridSize
            this.images[index].height = this.gridSize / aspectRatio
          } else {
            this.images[index].width = this.gridSize * aspectRatio
            this.images[index].height = this.gridSize
          }
        }
        this.saveToLocalStorage()
      }
      img.src = this.images[index].src
    },

    startResize(event, index) {
      if (!this.enableResize) return

      this.resizing = {
        active: true,
        imageIndex: index,
        startX: event.clientX,
        startY: event.clientY,
        startWidth: this.images[index].width,
        startHeight: this.images[index].height,
      }
      event.preventDefault()
    },

    handleResize(event) {
      if (!this.resizing.active) return

      const deltaX = event.clientX - this.resizing.startX
      const deltaY = event.clientY - this.resizing.startY

      const newWidth = Math.max(100, this.resizing.startWidth + deltaX)
      const newHeight = Math.max(100, this.resizing.startHeight + deltaY)

      if (this.resizing.imageIndex >= 0 && this.resizing.imageIndex < this.images.length) {
        this.images[this.resizing.imageIndex].width = newWidth
        this.images[this.resizing.imageIndex].height = newHeight
      }
    },

    stopResize() {
      if (this.resizing.active) {
        this.resizing.active = false
        this.saveToLocalStorage()
      }
    },

    toggleSelection(imageId) {
      const index = this.selectedImages.indexOf(imageId)
      if (index > -1) {
        this.selectedImages.splice(index, 1)
      } else {
        this.selectedImages.push(imageId)
      }
    },

    selectAll() {
      if (this.selectedImages.length === this.images.length) {
        this.selectedImages = []
      } else {
        this.selectedImages = this.images.map((img) => img.id)
      }
    },

    deleteSelected() {
      this.selectedImages.forEach((id) => {
        this.deleteImage(id)
      })
      this.selectedImages = []
    },

    deleteImage(imageId) {
      const index = this.images.findIndex((img) => img.id === imageId)
      if (index > -1) {
        this.currentStorageSize -= this.images[index].size
        this.images.splice(index, 1)
        this.saveToLocalStorage()

        // 从选中列表中移除
        const selectedIndex = this.selectedImages.indexOf(imageId)
        if (selectedIndex > -1) {
          this.selectedImages.splice(selectedIndex, 1)
        }
      }
    },

    clearAllImages() {
      this.images = []
      this.selectedImages = []
      this.currentStorageSize = 0
      this.saveToLocalStorage()
    },

    previewImage(image) {
      this.previewImageData = image
      this.previewDialog = true
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(1)) + ' ' + sizes[i]
    },

    saveToLocalStorage() {
      try {
        const data = {
          images: this.images,
          currentStorageSize: this.currentStorageSize,
          timestamp: new Date().toISOString(),
        }
        localStorage.setItem('imageGalleryData', JSON.stringify(data))
      } catch (error) {
        console.error('保存到本地存储失败:', error)
        this.$emit('message', { type: 'error', text: '保存失败，存储空间可能不足' })
      }
    },

    loadFromLocalStorage() {
      try {
        const data = localStorage.getItem('imageGalleryData')
        if (data) {
          const parsed = JSON.parse(data)
          this.images = parsed.images || []
          this.currentStorageSize = parsed.currentStorageSize || 0

          // 验证存储大小
          const calculatedSize = this.images.reduce((total, img) => total + img.size, 0)
          if (Math.abs(calculatedSize - this.currentStorageSize) > 1024) {
            this.currentStorageSize = calculatedSize
            this.saveToLocalStorage()
          }
        }
      } catch (error) {
        console.error('从本地存储加载失败:', error)
        this.images = []
        this.currentStorageSize = 0
      }
    },
  },
}
</script>

<style scoped>
.image-grid {
  display: grid;
  gap: 16px;
  min-height: 200px;
}

.image-item {
  position: relative;
  border: 2px solid transparent;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
  overflow: hidden;
  background: #f5f5f5;
}

.image-item:hover {
  border-color: #1976d2;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.image-item.selected {
  border-color: #1976d2;
  background: #e3f2fd;
}

.image-item.resizable .image-container {
  resize: both;
  overflow: hidden;
  min-width: 100px;
  min-height: 100px;
}

.image-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 2;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 4px;
}

.image-container {
  position: relative;
  width: 100%;
  height: 200px;
  overflow: hidden;
}

.image-display {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.2s ease;
}

.image-item:hover .image-display {
  transform: scale(1.05);
}

.resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 20px;
  background: #1976d2;
  cursor: nw-resize;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-item:hover .resize-handle {
  opacity: 0.7;
}

.resize-handle::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 8px;
  height: 8px;
  background: white;
  border-radius: 1px;
}

.image-info {
  padding: 8px;
  background: white;
}

.image-name {
  font-size: 12px;
  font-weight: 500;
  margin: 0 0 4px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.image-size {
  font-size: 10px;
  color: #666;
  margin: 0;
}

.image-actions {
  position: absolute;
  top: 8px;
  right: 8px;
  display: flex;
  gap: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-item:hover .image-actions {
  opacity: 1;
}

.image-actions .v-btn {
  background: rgba(255, 255, 255, 0.9);
}

/* 滚动条样式 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
</style>
