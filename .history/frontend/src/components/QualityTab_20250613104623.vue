<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <h3>Quality</h3>
      <v-btn color="primary" @click="showUploadDialog = true">
        <v-icon>mdi-plus</v-icon>
        Add Quality Image
      </v-btn>
    </div>

    <!-- 简洁的图片网格 -->
    <div class="image-grid">
      <div
        v-for="quality in qualities"
        :key="quality.id"
        class="image-item"
        :style="getImageItemStyle(quality.id)"
      >
        <!-- 图片本身 -->
        <img
          :src="quality.image_url"
          :style="getImageStyle(quality.id)"
          class="quality-image"
          @click="openImagePreview(quality)"
          @load="onImageLoad($event, quality.id)"
        />

        <!-- 悬停时显示的操作层 -->
        <div class="image-overlay">
          <!-- 标题 -->
          <div class="image-title">{{ quality.title || '无标题' }}</div>

          <!-- 操作按钮 -->
          <div class="image-actions">
            <v-btn
              icon="mdi-restore"
              size="small"
              variant="flat"
              color="white"
              @click.stop="resetToOriginalSize(quality.id)"
              title="恢复原始尺寸"
            ></v-btn>
            <v-btn
              icon="mdi-pencil"
              size="small"
              variant="flat"
              color="white"
              @click.stop="editQualityImage(quality)"
              title="编辑"
            ></v-btn>
            <v-btn
              icon="mdi-delete"
              size="small"
              variant="flat"
              color="error"
              @click.stop="deleteQualityImage(quality)"
              title="删除"
            ></v-btn>
          </div>
        </div>

        <!-- 拖拽调整大小控制点 -->
        <div class="resize-handle" @mousedown="startResize($event, quality.id)">
          <v-icon size="16" color="white">mdi-resize-bottom-right</v-icon>
        </div>

        <!-- 拖拽提示 -->
        <div class="resize-hint">拖拽调整大小</div>
      </div>
    </div>

    <!-- Empty State -->
    <div v-if="qualities.length === 0" class="text-center pa-8">
      <v-icon size="64" color="grey">mdi-image-outline</v-icon>
      <p class="text-grey mt-4">还没有上传质量相关图片</p>
      <p class="text-grey-lighten-1">点击上方按钮添加图片</p>
    </div>

    <!-- Upload Dialog -->
    <v-dialog v-model="showUploadDialog" max-width="600px" persistent>
      <v-card>
        <v-card-title>
          <span class="text-h5">{{ editingQuality ? '编辑质量图片' : '添加质量图片' }}</span>
        </v-card-title>
        <v-card-text>
          <v-text-field v-model="imageTitle" label="图片标题" required></v-text-field>

          <div class="upload-area" @click="triggerFileInput">
            <v-icon size="48" color="grey">mdi-cloud-upload</v-icon>
            <p class="mt-2 text-grey">点击选择图片文件</p>
          </div>

          <input
            ref="fileInput"
            type="file"
            accept="image/*"
            style="display: none"
            @change="handleFileSelect"
          />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="cancelUpload">取消</v-btn>
          <v-btn color="primary" @click="uploadImage" :loading="uploading">上传</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- Image Preview Dialog -->
    <v-dialog v-model="showPreviewDialog" max-width="90vw" max-height="90vh">
      <v-card v-if="previewingImage">
        <v-card-title>{{ previewingImage.title || '质量图片' }}</v-card-title>
        <v-card-text class="text-center">
          <img :src="previewingImage.image_url" style="max-width: 100%; max-height: 70vh" />
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="showPreviewDialog = false">关闭</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue'

const props = defineProps({
  releaseId: Number,
  qualities: Array,
})

const emit = defineEmits(['refresh'])

// 状态管理
const showUploadDialog = ref(false)
const showPreviewDialog = ref(false)
const uploading = ref(false)
const selectedFile = ref(null)
const imageTitle = ref('')
const editingQuality = ref(null)
const previewingImage = ref(null)
const fileInput = ref(null)

// 图片尺寸管理
const imageSizes = ref({}) // 存储每个图片的自定义尺寸
const originalImageSizes = ref({}) // 存储图片的原始尺寸
const isResizing = ref(false)
const resizingQualityId = ref(null)
const resizeStartData = ref({})

// 最小图片尺寸
const MIN_IMAGE_WIDTH = 150
const MIN_IMAGE_HEIGHT = 100

// 图片相关函数
function getImageSize(qualityId) {
  // 如果有自定义尺寸，使用自定义尺寸
  if (imageSizes.value[qualityId]) {
    return imageSizes.value[qualityId]
  }

  // 否则使用原始图片尺寸，但不小于最小尺寸
  const originalSize = originalImageSizes.value[qualityId]
  if (originalSize) {
    return {
      width: Math.max(MIN_IMAGE_WIDTH, originalSize.width),
      height: Math.max(MIN_IMAGE_HEIGHT, originalSize.height),
    }
  }

  // 默认尺寸
  return {
    width: MIN_IMAGE_WIDTH,
    height: MIN_IMAGE_HEIGHT,
  }
}

function getImageStyle(qualityId) {
  const size = getImageSize(qualityId)
  return {
    width: size.width + 'px',
    height: size.height + 'px',
    minWidth: MIN_IMAGE_WIDTH + 'px',
    minHeight: MIN_IMAGE_HEIGHT + 'px',
  }
}

function getImageItemStyle(qualityId) {
  const size = getImageSize(qualityId)
  return {
    width: size.width + 'px',
    height: size.height + 'px',
  }
}

function onImageLoad(event, qualityId) {
  const img = event.target
  originalImageSizes.value[qualityId] = {
    width: img.naturalWidth,
    height: img.naturalHeight,
  }

  // 如果还没有自定义尺寸，使用原始尺寸（但不小于最小尺寸）
  if (!imageSizes.value[qualityId]) {
    imageSizes.value[qualityId] = {
      width: Math.max(MIN_IMAGE_WIDTH, img.naturalWidth),
      height: Math.max(MIN_IMAGE_HEIGHT, img.naturalHeight),
    }
    saveImageSizes()
  }
}

function resetToOriginalSize(qualityId) {
  const originalSize = originalImageSizes.value[qualityId]
  if (originalSize) {
    imageSizes.value[qualityId] = {
      width: Math.max(MIN_IMAGE_WIDTH, originalSize.width),
      height: Math.max(MIN_IMAGE_HEIGHT, originalSize.height),
    }
    saveImageSizes()
  }
}

// 拖拽调整大小功能
function startResize(event, qualityId) {
  event.preventDefault()
  event.stopPropagation()

  isResizing.value = true
  resizingQualityId.value = qualityId
  resizeStartData.value = {
    startX: event.clientX,
    startY: event.clientY,
    startSize: { ...getImageSize(qualityId) },
  }

  document.addEventListener('mousemove', handleResizeMove)
  document.addEventListener('mouseup', handleResizeEnd)
  document.body.style.cursor = 'nw-resize'
  document.body.style.userSelect = 'none'
}

function handleResizeMove(event) {
  if (!isResizing.value || !resizingQualityId.value) return

  const { startX, startY, startSize } = resizeStartData.value
  const deltaX = event.clientX - startX
  const deltaY = event.clientY - startY

  const newWidth = Math.max(MIN_IMAGE_WIDTH, startSize.width + deltaX)
  const newHeight = Math.max(MIN_IMAGE_HEIGHT, startSize.height + deltaY)

  imageSizes.value[resizingQualityId.value] = {
    width: newWidth,
    height: newHeight,
  }
}

function handleResizeEnd() {
  if (isResizing.value) {
    isResizing.value = false
    resizingQualityId.value = null
    resizeStartData.value = {}

    document.removeEventListener('mousemove', handleResizeMove)
    document.removeEventListener('mouseup', handleResizeEnd)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''

    saveImageSizes()
  }
}

function loadImageSizes() {
  try {
    const savedSizes = localStorage.getItem('quality-image-sizes')
    if (savedSizes) {
      imageSizes.value = JSON.parse(savedSizes)
    }
  } catch (error) {
    console.warn('Failed to load image sizes from localStorage:', error)
  }
}

function saveImageSizes() {
  try {
    localStorage.setItem('quality-image-sizes', JSON.stringify(imageSizes.value))
  } catch (error) {
    console.warn('Failed to save image sizes to localStorage:', error)
  }
}

// 监听qualities变化
watch(
  () => props.qualities,
  (newQualities) => {
    if (newQualities && newQualities.length > 0) {
      for (const quality of newQualities) {
        if (!imageSizes.value[quality.id]) {
          // 初始化时使用默认尺寸，等图片加载后会更新为原始尺寸
          imageSizes.value[quality.id] = {
            width: MIN_IMAGE_WIDTH,
            height: MIN_IMAGE_HEIGHT,
          }
        }
      }
    }
  },
  { immediate: true }
)

// 基本函数
function openImagePreview(quality) {
  previewingImage.value = quality
  showPreviewDialog.value = true
}

function editQualityImage(quality) {
  editingQuality.value = quality
  imageTitle.value = quality.title || ''
  showUploadDialog.value = true
}

function deleteQualityImage(quality) {
  if (confirm('确定要删除这张图片吗？')) {
    // 删除逻辑
    console.log('删除图片:', quality.id)
  }
}

function triggerFileInput() {
  fileInput.value?.click()
}

function handleFileSelect(event) {
  const file = event.target.files[0]
  if (file) {
    selectedFile.value = file
    console.log('选择文件:', file.name)
  }
}

function cancelUpload() {
  showUploadDialog.value = false
  imageTitle.value = ''
  selectedFile.value = null
  editingQuality.value = null
}

function uploadImage() {
  console.log('上传图片')
  uploading.value = true

  setTimeout(() => {
    uploading.value = false
    cancelUpload()
    emit('refresh')
  }, 2000)
}

onMounted(() => {
  console.log('QualityTab mounted, qualities:', props.qualities)
  loadImageSizes()
})

onUnmounted(() => {
  document.removeEventListener('mousemove', handleResizeMove)
  document.removeEventListener('mouseup', handleResizeEnd)
  document.body.style.cursor = ''
  document.body.style.userSelect = ''
})
</script>

<style scoped>
/* 图片网格布局 */
.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: flex-start;
}

/* 图片项容器 */
.image-item {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  transition: all 0.2s ease;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.image-item:hover .image-overlay {
  opacity: 1;
}

.image-item:hover .resize-handle {
  opacity: 1;
}

.image-item:hover .resize-hint {
  opacity: 1;
}

/* 图片本身 */
.quality-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  display: block;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.quality-image:hover {
  transform: scale(1.02);
}

/* 悬停覆盖层 */
.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.7) 0%,
    rgba(0, 0, 0, 0.1) 50%,
    rgba(0, 0, 0, 0.7) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  padding: 12px;
  opacity: 0;
  transition: opacity 0.3s ease;
}

/* 图片标题 */
.image-title {
  color: white;
  font-size: 14px;
  font-weight: 500;
  text-shadow: 0 1px 3px rgba(0, 0, 0, 0.5);
  line-height: 1.2;
}

/* 操作按钮组 */
.image-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.image-actions .v-btn {
  backdrop-filter: blur(4px);
}

/* 拖拽调整大小控制点 */
.resize-handle {
  position: absolute;
  bottom: -8px;
  right: -8px;
  width: 24px;
  height: 24px;
  background: rgba(0, 0, 0, 0.7);
  border: 2px solid white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: nw-resize;
  opacity: 0;
  transition: all 0.2s ease;
  z-index: 10;
  backdrop-filter: blur(4px);
}

.resize-handle:hover {
  background: rgba(0, 0, 0, 0.9);
  transform: scale(1.1);
}

/* 拖拽提示 */
.resize-hint {
  position: absolute;
  bottom: 8px;
  left: 8px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.2s ease;
  pointer-events: none;
  backdrop-filter: blur(4px);
}

/* 上传区域 */
.upload-area {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 24px;
  text-align: center;
  cursor: pointer;
  transition: border-color 0.3s ease;
}

.upload-area:hover {
  border-color: #2196f3;
}
</style>
