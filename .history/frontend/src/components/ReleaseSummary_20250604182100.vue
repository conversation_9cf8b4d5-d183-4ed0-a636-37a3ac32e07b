<template>
  <div class="release-summary">
    <div class="mb-4">
      <h2 class="text-h4 mb-2">Release Summary</h2>
      <p class="text-subtitle-1 text-grey-darken-1">Overview of all releases in the system</p>
    </div>

    <!-- Summary Cards -->
    <v-row class="mb-6">
      <v-col cols="12" sm="6" md="3">
        <v-card class="text-center" color="blue-lighten-5">
          <v-card-text>
            <v-icon size="40" color="blue">mdi-rocket-launch</v-icon>
            <div class="text-h3 font-weight-bold text-blue mt-2">{{ totalReleases }}</div>
            <div class="text-body-2 text-grey-darken-1">Total Releases</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card class="text-center" color="green-lighten-5">
          <v-card-text>
            <v-icon size="40" color="green">mdi-check-circle</v-icon>
            <div class="text-h3 font-weight-bold text-green mt-2">{{ completedReleases }}</div>
            <div class="text-body-2 text-grey-darken-1">Completed</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card class="text-center" color="orange-lighten-5">
          <v-card-text>
            <v-icon size="40" color="orange">mdi-progress-clock</v-icon>
            <div class="text-h3 font-weight-bold text-orange mt-2">{{ inProgressReleases }}</div>
            <div class="text-body-2 text-grey-darken-1">In Progress</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" md="3">
        <v-card class="text-center" color="grey-lighten-4">
          <v-card-text>
            <v-icon size="40" color="grey">mdi-clock-outline</v-icon>
            <div class="text-h3 font-weight-bold text-grey-darken-2 mt-2">
              {{ planningReleases }}
            </div>
            <div class="text-body-2 text-grey-darken-1">Planning</div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Release Table -->
    <v-card>
      <v-card-title class="d-flex justify-space-between align-center">
        <span>All Releases</span>
        <v-btn color="primary" @click="$emit('createRelease')">
          <v-icon>mdi-plus</v-icon>
          New Release
        </v-btn>
      </v-card-title>

      <v-data-table
        :headers="headers"
        :items="releases"
        :loading="loading"
        item-value="id"
        class="elevation-0"
        @click:row="(_, { item }) => $emit('selectRelease', item)"
        hover
      >
        <template v-slot:item.state="{ item }">
          <v-chip :color="getStatusColor(item.state)" size="small" variant="flat">
            {{ item.state || 'No Status' }}
          </v-chip>
        </template>

        <template v-slot:item.start_date="{ item }">
          {{ formatDate(item.start_date) }}
        </template>

        <template v-slot:item.end_date="{ item }">
          {{ formatDate(item.end_date) }}
        </template>

        <template v-slot:item.actions="{ item }">
          <v-btn icon size="small" variant="text" @click.stop="$emit('editRelease', item)">
            <v-icon>mdi-pencil</v-icon>
          </v-btn>
          <v-btn
            icon
            size="small"
            variant="text"
            color="error"
            @click.stop="$emit('deleteRelease', item)"
          >
            <v-icon>mdi-delete</v-icon>
          </v-btn>
        </template>

        <template v-slot:no-data>
          <div class="text-center pa-8">
            <v-icon size="64" color="grey">mdi-rocket-launch-outline</v-icon>
            <div class="text-h6 mt-4 text-grey">No releases found</div>
            <p class="text-grey">Create your first release to get started</p>
          </div>
        </template>
      </v-data-table>
    </v-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  releases: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['selectRelease', 'createRelease', 'editRelease', 'deleteRelease'])

const headers = [
  { title: 'Name', key: 'name', sortable: true },
  { title: 'Status', key: 'state', sortable: true },
  { title: 'Version', key: 'version', sortable: true },
  { title: 'Start Date', key: 'start_date', sortable: true },
  { title: 'End Date', key: 'end_date', sortable: true },
  { title: 'Actions', key: 'actions', sortable: false, width: '120px' },
]

const totalReleases = computed(() => props.releases.length)

const completedReleases = computed(
  () =>
    props.releases.filter(
      (r) => r.state?.toLowerCase() === 'completed' || r.state?.toLowerCase() === 'release'
    ).length
)

const inProgressReleases = computed(
  () =>
    props.releases.filter((r) =>
      ['development', 'testing', 'staging'].includes(r.state?.toLowerCase())
    ).length
)

const planningReleases = computed(
  () => props.releases.filter((r) => r.state?.toLowerCase() === 'planning' || !r.state).length
)

function getStatusColor(status) {
  const statusColors = {
    planning: 'blue',
    development: 'orange',
    testing: 'purple',
    staging: 'cyan',
    release: 'green',
    completed: 'teal',
    cancelled: 'red',
  }
  return statusColors[status?.toLowerCase()] || 'grey'
}

function formatDate(dateString) {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  } catch {
    return '-'
  }
}
</script>

<style scoped>
.release-summary {
  max-width: 100%;
}

.v-data-table tbody tr {
  cursor: pointer;
}

.v-data-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.04);
}
</style>
