<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <h3>Quality</h3>
      <v-btn color="primary" @click="openCreateDialog">
        <v-icon>mdi-plus</v-icon>
        Add Quality
      </v-btn>
    </div>

    <!-- 图片上传区域 -->
    <v-card class="mb-4 pa-4" v-if="showUploadArea">
      <v-card-title class="text-h6">Upload Images</v-card-title>
      <v-card-text>
        <!-- 拖拽上传区域 -->
        <div
          ref="dropZone"
          @drop="handleFileDrop"
          @dragover.prevent
          @dragenter.prevent
          class="upload-zone"
          :class="{ 'upload-zone--dragover': isDragging }"
        >
          <v-icon size="48" color="grey">mdi-cloud-upload</v-icon>
          <p class="text-h6 mt-2">Drag & Drop images here</p>
          <p class="text-body-2 text-grey">or</p>
          <v-btn color="primary" @click="triggerFileInput">
            <v-icon left>mdi-upload</v-icon>
            Choose Files
          </v-btn>
          <p class="text-caption text-grey mt-2">Supports JPG, PNG, GIF up to 5MB each</p>
          <p class="text-caption text-grey">You can also paste images directly (Ctrl+V)</p>
        </div>

        <!-- 隐藏的文件输入 -->
        <input
          ref="fileInput"
          type="file"
          multiple
          accept=".jpg,.jpeg,.png,.gif"
          @change="handleFileSelect"
          style="display: none"
        />
      </v-card-text>
    </v-card>

    <!-- Quality列表 -->
    <div v-if="qualities && qualities.length > 0">
      <v-row>
        <v-col v-for="quality in qualities" :key="quality.id" cols="12" md="6" lg="4" xl="3">
          <v-card class="quality-card">
            <v-card-title class="d-flex justify-space-between align-center">
              <span>{{ quality.title || `Quality #${quality.id}` }}</span>
              <div>
                <v-btn icon size="small" @click="editQuality(quality)" class="mr-2">
                  <v-icon>mdi-pencil</v-icon>
                </v-btn>
                <v-btn icon size="small" color="error" @click="deleteQuality(quality)">
                  <v-icon>mdi-delete</v-icon>
                </v-btn>
              </div>
            </v-card-title>

            <v-card-text v-if="quality.description">
              <p class="text-body-2">{{ quality.description }}</p>
            </v-card-text>

            <!-- 图片网格 -->
            <div v-if="quality.images && quality.images.length > 0" class="image-grid pa-3">
              <div
                v-for="image in quality.images"
                :key="image.id"
                class="image-container"
                :style="{
                  width: getImageDisplayWidth(image) + 'px',
                  height: getImageDisplayHeight(image) + 'px',
                }"
              >
                <img
                  :src="`${API_BASE_URL}/uploads/quality-images/${image.filename}`"
                  :alt="image.original_name"
                  class="quality-image"
                  @click="openImagePreview(image)"
                />
                <!-- 拖拽调整大小的控制点 -->
                <div class="resize-handle" @mousedown="startResize($event, image)"></div>
                <!-- 删除按钮 -->
                <v-btn
                  icon
                  size="x-small"
                  color="error"
                  class="delete-image-btn"
                  @click="deleteImage(image)"
                >
                  <v-icon size="small">mdi-close</v-icon>
                </v-btn>
              </div>
            </div>

            <!-- 无图片时的占位符 -->
            <div v-else class="pa-4 text-center">
              <v-icon size="64" color="grey-lighten-2">mdi-image-outline</v-icon>
              <p class="text-grey mt-2">No images uploaded</p>
              <v-btn color="primary" variant="outlined" @click="openImageUpload(quality)">
                <v-icon left>mdi-upload</v-icon>
                Upload Images
              </v-btn>
            </div>
          </v-card>
        </v-col>
      </v-row>
    </div>

    <!-- 无Quality时的空状态 -->
    <div v-else class="text-center pa-8">
      <v-icon size="64" color="grey">mdi-image-outline</v-icon>
      <h4 class="text-h6 mt-4 text-grey">No quality records yet</h4>
      <p class="text-grey">Create a quality record to start uploading images</p>
    </div>

    <!-- 创建/编辑Quality对话框 -->
    <v-dialog v-model="showQualityDialog" max-width="500px">
      <v-card>
        <v-card-title>{{ editingQuality ? 'Edit Quality' : 'Create Quality' }}</v-card-title>
        <v-card-text>
          <v-text-field
            v-model="qualityForm.title"
            label="Title"
            variant="outlined"
            class="mb-3"
          ></v-text-field>
          <v-textarea
            v-model="qualityForm.description"
            label="Description"
            variant="outlined"
            rows="3"
          ></v-textarea>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="closeQualityDialog">Cancel</v-btn>
          <v-btn color="primary" @click="saveQuality" :loading="saving">
            {{ editingQuality ? 'Update' : 'Create' }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>

    <!-- 图片预览对话框 -->
    <v-dialog v-model="showImagePreview" max-width="800px">
      <v-card v-if="previewImage">
        <v-card-title class="d-flex justify-space-between align-center">
          <span>{{ previewImage.original_name }}</span>
          <v-btn icon @click="showImagePreview = false">
            <v-icon>mdi-close</v-icon>
          </v-btn>
        </v-card-title>
        <v-card-text class="pa-0">
          <img
            :src="`${API_BASE_URL}/uploads/quality-images/${previewImage.filename}`"
            :alt="previewImage.original_name"
            style="width: 100%; height: auto; max-height: 80vh; object-fit: contain"
          />
        </v-card-text>
        <v-card-text>
          <v-chip size="small" class="mr-2">{{ formatFileSize(previewImage.file_size) }}</v-chip>
          <v-chip size="small" class="mr-2"
            >{{ previewImage.width }}×{{ previewImage.height }}</v-chip
          >
          <v-chip size="small">{{ previewImage.mime_type }}</v-chip>
        </v-card-text>
      </v-card>
    </v-dialog>

    <!-- 删除确认对话框 -->
    <v-dialog v-model="showDeleteDialog" max-width="400px">
      <v-card>
        <v-card-title>Confirm Delete</v-card-title>
        <v-card-text
          >Are you sure you want to delete this quality record and all its images?</v-card-text
        >
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="showDeleteDialog = false">Cancel</v-btn>
          <v-btn color="error" @click="confirmDelete" :loading="deleting">Delete</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

const props = defineProps({
  releaseId: Number,
  qualities: Array,
})

const emit = defineEmits(['refresh'])

// API基础URL
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:9001'

// 响应式状态
const showUploadArea = ref(false)
const showQualityDialog = ref(false)
const showImagePreview = ref(false)
const showDeleteDialog = ref(false)
const isDragging = ref(false)
const saving = ref(false)
const deleting = ref(false)

const editingQuality = ref(null)
const qualityToDelete = ref(null)
const previewImage = ref(null)
const currentQuality = ref(null) // 当前要上传图片的quality

// 表单数据
const qualityForm = ref({
  title: '',
  description: '',
})

// 图片尺寸存储（用于拖拽调整）
const imageSizes = ref({})

// DOM引用
const dropZone = ref(null)
const fileInput = ref(null)

// 计算属性
const hasQualities = computed(() => props.qualities && props.qualities.length > 0)

// 生命周期
onMounted(() => {
  // 监听全局粘贴事件
  document.addEventListener('paste', handlePaste)

  // 监听拖拽事件
  document.addEventListener('dragover', preventDefault)
  document.addEventListener('drop', preventDefault)
})

onUnmounted(() => {
  document.removeEventListener('paste', handlePaste)
  document.removeEventListener('dragover', preventDefault)
  document.removeEventListener('drop', preventDefault)
})

// 事件处理函数
function preventDefault(e) {
  e.preventDefault()
}

function openCreateDialog() {
  editingQuality.value = null
  qualityForm.value = { title: '', description: '' }
  showQualityDialog.value = true
}

function editQuality(quality) {
  editingQuality.value = quality
  qualityForm.value = {
    title: quality.title || '',
    description: quality.description || '',
  }
  showQualityDialog.value = true
}

function closeQualityDialog() {
  showQualityDialog.value = false
  editingQuality.value = null
}

async function saveQuality() {
  saving.value = true
  try {
    const url = editingQuality.value
      ? `${API_BASE_URL}/api/qualities/${editingQuality.value.id}`
      : `${API_BASE_URL}/api/releases/${props.releaseId}/qualities`

    const method = editingQuality.value ? 'PUT' : 'POST'

    const response = await fetch(url, {
      method,
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(qualityForm.value),
    })

    if (!response.ok) throw new Error('Failed to save quality')

    closeQualityDialog()
    emit('refresh')
  } catch (error) {
    console.error('Error saving quality:', error)
    alert('Failed to save quality')
  } finally {
    saving.value = false
  }
}

function deleteQuality(quality) {
  qualityToDelete.value = quality
  showDeleteDialog.value = true
}

async function confirmDelete() {
  deleting.value = true
  try {
    const response = await fetch(`${API_BASE_URL}/api/qualities/${qualityToDelete.value.id}`, {
      method: 'DELETE',
    })

    if (!response.ok) throw new Error('Failed to delete quality')

    showDeleteDialog.value = false
    emit('refresh')
  } catch (error) {
    console.error('Error deleting quality:', error)
    alert('Failed to delete quality')
  } finally {
    deleting.value = false
  }
}

function openImageUpload(quality) {
  currentQuality.value = quality
  showUploadArea.value = true
  nextTick(() => {
    dropZone.value?.scrollIntoView({ behavior: 'smooth' })
  })
}

function triggerFileInput() {
  fileInput.value?.click()
}

function handleFileSelect(event) {
  const files = Array.from(event.target.files)
  uploadFiles(files)
}

function handleFileDrop(event) {
  event.preventDefault()
  isDragging.value = false
  const files = Array.from(event.dataTransfer.files).filter((file) =>
    file.type.startsWith('image/')
  )
  uploadFiles(files)
}

function handlePaste(event) {
  const items = event.clipboardData?.items
  if (!items) return

  for (const item of items) {
    if (item.type.startsWith('image/')) {
      event.preventDefault()
      const file = item.getAsFile()
      if (file) {
        // 如果没有选择quality，自动创建一个
        if (!currentQuality.value && hasQualities.value) {
          currentQuality.value = props.qualities[0]
        }

        if (!currentQuality.value) {
          alert('Please create a quality record first')
          return
        }

        uploadFiles([file])
      }
      break
    }
  }
}

async function uploadFiles(files) {
  if (!currentQuality.value) {
    alert('Please select a quality record first')
    return
  }

  for (const file of files) {
    if (!validateFile(file)) continue

    const formData = new FormData()
    formData.append('image', file)

    try {
      const response = await fetch(
        `${API_BASE_URL}/api/qualities/${currentQuality.value.id}/images`,
        {
          method: 'POST',
          body: formData,
        }
      )

      if (!response.ok) throw new Error('Upload failed')

      emit('refresh')
    } catch (error) {
      console.error('Error uploading file:', error)
      alert(`Failed to upload ${file.name}`)
    }
  }
}

function validateFile(file) {
  // 检查文件类型
  if (!file.type.startsWith('image/')) {
    alert(`${file.name} is not an image file`)
    return false
  }

  // 检查文件大小 (5MB)
  const maxSize = 5 * 1024 * 1024
  if (file.size > maxSize) {
    alert(`${file.name} exceeds 5MB limit`)
    return false
  }

  return true
}

async function deleteImage(image) {
  if (!confirm(`Delete ${image.original_name}?`)) return

  try {
    const response = await fetch(`${API_BASE_URL}/api/qualities/images/${image.id}`, {
      method: 'DELETE',
    })

    if (!response.ok) throw new Error('Failed to delete image')

    emit('refresh')
  } catch (error) {
    console.error('Error deleting image:', error)
    alert('Failed to delete image')
  }
}

function openImagePreview(image) {
  previewImage.value = image
  showImagePreview.value = true
}

// 图片尺寸相关函数
function getImageDisplayWidth(image) {
  const key = `${image.quality_id}-${image.id}`
  return imageSizes.value[key]?.width || Math.min(image.width || 200, 300)
}

function getImageDisplayHeight(image) {
  const key = `${image.quality_id}-${image.id}`
  return imageSizes.value[key]?.height || Math.min(image.height || 150, 200)
}

function startResize(event, image) {
  event.preventDefault()
  const key = `${image.quality_id}-${image.id}`

  const startX = event.clientX
  const startY = event.clientY
  const startWidth = getImageDisplayWidth(image)
  const startHeight = getImageDisplayHeight(image)

  function doResize(e) {
    const newWidth = Math.max(100, startWidth + (e.clientX - startX))
    const newHeight = Math.max(75, startHeight + (e.clientY - startY))

    if (!imageSizes.value[key]) {
      imageSizes.value[key] = {}
    }
    imageSizes.value[key].width = newWidth
    imageSizes.value[key].height = newHeight
  }

  function stopResize() {
    document.removeEventListener('mousemove', doResize)
    document.removeEventListener('mouseup', stopResize)
  }

  document.addEventListener('mousemove', doResize)
  document.addEventListener('mouseup', stopResize)
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes'
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

// 拖拽状态管理
dropZone.value?.addEventListener?.('dragenter', () => {
  isDragging.value = true
})
dropZone.value?.addEventListener?.('dragleave', (e) => {
  if (!e.relatedTarget || !dropZone.value?.contains(e.relatedTarget)) {
    isDragging.value = false
  }
})
</script>

<style scoped>
.upload-zone {
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 40px;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.upload-zone--dragover {
  border-color: #1976d2;
  background-color: #f3f9ff;
}

.upload-zone:hover {
  border-color: #1976d2;
}

.quality-card {
  height: 100%;
}

.image-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
}

.image-container {
  position: relative;
  border-radius: 8px;
  overflow: hidden;
  min-width: 100px;
  min-height: 75px;
}

.quality-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.quality-image:hover {
  transform: scale(1.02);
}

.resize-handle {
  position: absolute;
  bottom: 0;
  right: 0;
  width: 12px;
  height: 12px;
  background: #1976d2;
  cursor: nw-resize;
  border-radius: 4px 0 0 0;
}

.resize-handle:hover {
  background: #1565c0;
}

.delete-image-btn {
  position: absolute;
  top: 4px;
  right: 4px;
  background: rgba(255, 255, 255, 0.9) !important;
}

.delete-image-btn:hover {
  background: rgba(255, 255, 255, 1) !important;
}
</style>
