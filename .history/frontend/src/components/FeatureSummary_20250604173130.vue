<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <h3>Feature Summary</h3>
      <v-btn color="primary" @click="showCreateDialog = true">
        <v-icon>mdi-plus</v-icon>
        Add Feature
      </v-btn>
    </div>

    <v-data-table :headers="headers" :items="features" class="elevation-1">
      <template v-slot:item.actions="{ item }">
        <v-btn size="small" icon @click="editFeature(item)">
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
        <v-btn size="small" icon @click="deleteFeature(item)">
          <v-icon>mdi-delete</v-icon>
        </v-btn>
      </template>
    </v-data-table>

    <!-- Feature Dialog -->
    <v-dialog v-model="showCreateDialog" max-width="600">
      <v-card>
        <v-card-title>{{ editingFeature?.id ? 'Edit Feature' : 'Add Feature' }}</v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field v-model="formData.name" label="Name" required></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field v-model="formData.owner" label="Owner"></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12">
                <v-textarea
                  v-model="formData.description"
                  label="Description"
                  rows="3"
                ></v-textarea>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-select v-model="formData.state" label="State" :items="stateOptions"></v-select>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.started"
                  label="Started Date"
                  type="date"
                  hint="When development started"
                ></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.dev_done"
                  label="Dev Done Date"
                  type="date"
                  hint="When development completed"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.ended"
                  label="End Date"
                  type="date"
                  hint="When feature was fully completed"
                ></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-text-field v-model="formData.design_spec" label="Design Spec URL"></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.test_strategy"
                  label="Test Strategy URL"
                ></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12">
                <v-text-field v-model="formData.test_report" label="Test Report URL"></v-text-field>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="showCreateDialog = false">Cancel</v-btn>
          <v-btn color="primary" @click="saveFeature">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue'
import { useReleaseStore } from '@/stores/release'

const props = defineProps({
  releaseId: Number,
  features: Array,
})

const releaseStore = useReleaseStore()
const showCreateDialog = ref(false)
const editingFeature = ref(null)
const formData = ref({
  name: '',
  description: '',
  owner: '',
  state: '',
  started: '',
  dev_done: '',
  ended: '',
  design_spec: '',
  test_strategy: '',
  test_report: '',
})

const headers = [
  { title: 'Name', key: 'name' },
  { title: 'Description', key: 'description' },
  { title: 'Owner', key: 'owner' },
  { title: 'State', key: 'state' },
  {
    title: 'Started',
    key: 'started',
    value: (item) => (item.started ? new Date(item.started).toLocaleDateString() : '-'),
  },
  {
    title: 'Dev Done',
    key: 'dev_done',
    value: (item) => (item.dev_done ? new Date(item.dev_done).toLocaleDateString() : '-'),
  },
  {
    title: 'Ended',
    key: 'ended',
    value: (item) => (item.ended ? new Date(item.ended).toLocaleDateString() : '-'),
  },
  { title: 'Actions', key: 'actions', sortable: false },
]

const stateOptions = ['Planning', 'Development', 'Testing', 'Done']

function editFeature(feature) {
  editingFeature.value = feature
  formData.value = { ...feature }
  showCreateDialog.value = true
}

function deleteFeature(feature) {
  // Implementation for delete
}

async function saveFeature() {
  if (editingFeature.value?.id) {
    await releaseStore.updateFeature(editingFeature.value.id, formData.value)
  } else {
    await releaseStore.createFeature(props.releaseId, formData.value)
  }
  showCreateDialog.value = false
  editingFeature.value = null
  formData.value = { name: '', description: '', owner: '', state: '' }
}
</script>
