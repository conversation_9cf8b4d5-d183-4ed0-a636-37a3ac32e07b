<template>
  <v-container>
    <!-- Release Name -->
    <v-row class="mb-4">
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-icon class="mr-2">mdi-rocket-launch</v-icon>
            <span>{{ release.name }}</span>
            <v-spacer></v-spacer>
            <v-chip v-if="release.state" :color="getStatusColor(release.state)" size="small">
              {{ release.state }}
            </v-chip>
          </v-card-title>
        </v-card>
      </v-col>
    </v-row>

    <!-- MT0 - PRD阶段 -->
    <v-row class="mb-4">
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-chip color="primary" size="small" class="mr-3">MT0</v-chip>
            <span>PRD 阶段</span>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-list>
                  <v-list-item>
                    <v-list-item-title>PRD Signoff Date</v-list-item-title>
                    <v-list-item-subtitle>{{
                      formatDate(release.prd_signoff) || 'Not set'
                    }}</v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
              <v-col cols="12" md="6">
                <v-list>
                  <v-list-item v-if="release.prd_link">
                    <v-list-item-title>PRD Link</v-list-item-title>
                    <v-list-item-subtitle>
                      <a :href="release.prd_link" target="_blank" class="text-primary">
                        <v-icon size="small" class="mr-1">mdi-link</v-icon>
                        查看 PRD 文档
                      </a>
                    </v-list-item-subtitle>
                  </v-list-item>
                  <v-list-item v-else>
                    <v-list-item-title>PRD Link</v-list-item-title>
                    <v-list-item-subtitle class="text-grey">Not set</v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- MT1 - 测试策略阶段 -->
    <v-row class="mb-4">
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-chip color="secondary" size="small" class="mr-3">MT1</v-chip>
            <span>测试策略阶段</span>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="4">
                <v-list>
                  <v-list-item>
                    <v-list-item-title>Test Strategy Signoff</v-list-item-title>
                    <v-list-item-subtitle>{{
                      formatDate(release.test_strategy_signoff) || 'Not set'
                    }}</v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
              <v-col cols="12" md="4">
                <v-list>
                  <v-list-item v-if="release.test_strategy_link">
                    <v-list-item-title>Test Strategy Link</v-list-item-title>
                    <v-list-item-subtitle>
                      <a :href="release.test_strategy_link" target="_blank" class="text-primary">
                        <v-icon size="small" class="mr-1">mdi-link</v-icon>
                        查看测试策略
                      </a>
                    </v-list-item-subtitle>
                  </v-list-item>
                  <v-list-item v-else>
                    <v-list-item-title>Test Strategy Link</v-list-item-title>
                    <v-list-item-subtitle class="text-grey">Not set</v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
              <v-col cols="12" md="4">
                <v-list>
                  <v-list-item>
                    <v-list-item-title>Release Branch Off</v-list-item-title>
                    <v-list-item-subtitle>{{
                      formatDate(release.release_branch_off) || 'Not set'
                    }}</v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- MT2 - 代码冻结阶段 -->
    <v-row class="mb-4">
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-chip color="warning" size="small" class="mr-3">MT2</v-chip>
            <span>代码冻结阶段</span>
          </v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item>
                <v-list-item-title>Release Code Freeze</v-list-item-title>
                <v-list-item-subtitle>{{
                  formatDate(release.release_code_freeze) || 'Not set'
                }}</v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- MT3 - 发布阶段 -->
    <v-row class="mb-4">
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-chip color="success" size="small" class="mr-3">MT3</v-chip>
            <span>发布阶段</span>
          </v-card-title>
          <v-card-text>
            <v-row>
              <v-col cols="12" md="6">
                <v-list>
                  <v-list-item>
                    <v-list-item-title>Release State</v-list-item-title>
                    <v-list-item-subtitle>
                      <v-chip
                        v-if="release.state"
                        :color="getStatusColor(release.state)"
                        size="small"
                      >
                        {{ release.state }}
                      </v-chip>
                      <span v-else class="text-grey">Not set</span>
                    </v-list-item-subtitle>
                  </v-list-item>
                  <v-list-item>
                    <v-list-item-title>Risk State</v-list-item-title>
                    <v-list-item-subtitle>
                      <v-chip
                        v-if="release.risk_state"
                        :color="getRiskColor(release.risk_state)"
                        size="small"
                      >
                        {{ release.risk_state }}
                      </v-chip>
                      <span v-else class="text-grey">Not set</span>
                    </v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
              <v-col cols="12" md="6">
                <v-list>
                  <v-list-item v-if="release.risk_link || release.risk">
                    <v-list-item-title>Risk Link</v-list-item-title>
                    <v-list-item-subtitle>
                      <a
                        v-if="release.risk_link"
                        :href="release.risk_link"
                        target="_blank"
                        class="text-primary"
                      >
                        <v-icon size="small" class="mr-1">mdi-link</v-icon>
                        查看风险评估
                      </a>
                      <span v-else-if="release.risk">{{ release.risk }}</span>
                    </v-list-item-subtitle>
                  </v-list-item>
                  <v-list-item v-else>
                    <v-list-item-title>Risk Link</v-list-item-title>
                    <v-list-item-subtitle class="text-grey">Not set</v-list-item-subtitle>
                  </v-list-item>

                  <v-list-item v-if="release.software_download || release.softwares">
                    <v-list-item-title>Software Download</v-list-item-title>
                    <v-list-item-subtitle>
                      <a
                        v-if="release.software_download"
                        :href="release.software_download"
                        target="_blank"
                        class="text-primary"
                      >
                        <v-icon size="small" class="mr-1">mdi-download</v-icon>
                        下载软件
                      </a>
                      <span v-else-if="release.softwares">{{ release.softwares }}</span>
                    </v-list-item-subtitle>
                  </v-list-item>
                  <v-list-item v-else>
                    <v-list-item-title>Software Download</v-list-item-title>
                    <v-list-item-subtitle class="text-grey">Not set</v-list-item-subtitle>
                  </v-list-item>

                  <v-list-item v-if="release.doc_link || release.docs">
                    <v-list-item-title>Documentation</v-list-item-title>
                    <v-list-item-subtitle>
                      <a
                        v-if="release.doc_link"
                        :href="release.doc_link"
                        target="_blank"
                        class="text-primary"
                      >
                        <v-icon size="small" class="mr-1">mdi-file-document</v-icon>
                        查看文档
                      </a>
                      <span v-else-if="release.docs">{{ release.docs }}</span>
                    </v-list-item-subtitle>
                  </v-list-item>
                  <v-list-item v-else>
                    <v-list-item-title>Documentation</v-list-item-title>
                    <v-list-item-subtitle class="text-grey">Not set</v-list-item-subtitle>
                  </v-list-item>
                </v-list>
              </v-col>
            </v-row>

            <v-row v-if="release.test_report">
              <v-col cols="12">
                <v-divider class="mb-3"></v-divider>
                <h4 class="mb-2">Test Report</h4>
                <p class="text-body-2">{{ release.test_report }}</p>
              </v-col>
            </v-row>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- MT4 - 总结阶段 -->
    <v-row v-if="release.lessons" class="mb-4">
      <v-col cols="12">
        <v-card>
          <v-card-title class="d-flex align-center">
            <v-chip color="info" size="small" class="mr-3">MT4</v-chip>
            <span>总结阶段</span>
          </v-card-title>
          <v-card-text>
            <h4 class="mb-2">Lessons Learned</h4>
            <p class="text-body-2">{{ release.lessons }}</p>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- 时间轴 -->
    <v-row>
      <v-col cols="12">
        <v-card>
          <v-card-title>发布时间轴</v-card-title>
          <v-card-text>
            <v-timeline side="end" density="compact">
              <v-timeline-item v-if="release.prd_signoff" dot-color="primary" size="small">
                <div class="d-flex">
                  <strong>PRD Signoff</strong>
                  <v-spacer></v-spacer>
                  <span>{{ formatDate(release.prd_signoff) }}</span>
                </div>
              </v-timeline-item>

              <v-timeline-item
                v-if="release.test_strategy_signoff"
                dot-color="secondary"
                size="small"
              >
                <div class="d-flex">
                  <strong>Test Strategy Signoff</strong>
                  <v-spacer></v-spacer>
                  <span>{{ formatDate(release.test_strategy_signoff) }}</span>
                </div>
              </v-timeline-item>

              <v-timeline-item v-if="release.release_branch_off" dot-color="secondary" size="small">
                <div class="d-flex">
                  <strong>Release Branch Off</strong>
                  <v-spacer></v-spacer>
                  <span>{{ formatDate(release.release_branch_off) }}</span>
                </div>
              </v-timeline-item>

              <v-timeline-item v-if="release.release_code_freeze" dot-color="warning" size="small">
                <div class="d-flex">
                  <strong>Code Freeze</strong>
                  <v-spacer></v-spacer>
                  <span>{{ formatDate(release.release_code_freeze) }}</span>
                </div>
              </v-timeline-item>
            </v-timeline>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { defineProps } from 'vue'

defineProps({
  release: {
    type: Object,
    required: true,
  },
})

function formatDate(dateString) {
  if (!dateString) return null
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

function getStatusColor(status) {
  const statusColors = {
    planning: 'blue',
    development: 'orange',
    testing: 'purple',
    staging: 'cyan',
    release: 'green',
    completed: 'teal',
    cancelled: 'red',
  }
  return statusColors[status?.toLowerCase()] || 'grey'
}

function getRiskColor(riskState) {
  const riskColors = {
    low: 'green',
    medium: 'orange',
    high: 'red',
    critical: 'red-darken-2',
  }
  return riskColors[riskState?.toLowerCase()] || 'grey'
}
</script>

<style scoped>
.v-timeline {
  padding-left: 16px;
}

.v-card-title {
  font-weight: 600;
}

.v-chip {
  font-weight: 600;
}
</style>
