<template>
  <v-dialog :model-value="modelValue" @update:model-value="$emit('update:modelValue', $event)" max-width="800">
    <v-card>
      <v-card-title>{{ release?.id ? 'Edit Release' : 'Create New Release' }}</v-card-title>
      
      <v-card-text>
        <v-form ref="form" v-model="valid">
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.name"
                label="Release Name"
                :rules="[v => !!v || 'Name is required']"
                required
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-select
                v-model="formData.state"
                label="State"
                :items="stateOptions"
              ></v-select>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.prd_signoff"
                label="PRD Signoff Date"
                type="date"
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.prd_link"
                label="PRD Link"
                type="url"
              ></v-text-field>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.test_strategy_signoff"
                label="Test Strategy Signoff Date"
                type="date"
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.test_strategy_link"
                label="Test Strategy Link"
                type="url"
              ></v-text-field>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.release_branch_off"
                label="Release Branch Off Date"
                type="date"
              ></v-text-field>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.release_code_freeze"
                label="Release Code Freeze Date"
                type="date"
              ></v-text-field>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12" md="6">
              <v-select
                v-model="formData.risk_state"
                label="Risk State"
                :items="riskStateOptions"
              ></v-select>
            </v-col>
            
            <v-col cols="12" md="6">
              <v-text-field
                v-model="formData.softwares"
                label="Softwares"
              ></v-text-field>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <v-textarea
                v-model="formData.test_report"
                label="Test Report"
                rows="3"
              ></v-textarea>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <v-textarea
                v-model="formData.risk"
                label="Risk"
                rows="3"
              ></v-textarea>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <v-textarea
                v-model="formData.docs"
                label="Documentation"
                rows="3"
              ></v-textarea>
            </v-col>
          </v-row>
          
          <v-row>
            <v-col cols="12">
              <v-textarea
                v-model="formData.lessons"
                label="Lessons Learned"
                rows="3"
              ></v-textarea>
            </v-col>
          </v-row>
        </v-form>
      </v-card-text>
      
      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn @click="$emit('update:modelValue', false)">Cancel</v-btn>
        <v-btn color="primary" @click="save" :disabled="!valid">Save</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, watch, defineProps, defineEmits } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  release: Object
})

const emit = defineEmits(['update:modelValue', 'save'])

const form = ref(null)
const valid = ref(false)

const formData = ref({
  name: '',
  state: '',
  prd_signoff: '',
  prd_link: '',
  test_strategy_signoff: '',
  test_strategy_link: '',
  release_branch_off: '',
  release_code_freeze: '',
  risk_state: '',
  test_report: '',
  risk: '',
  softwares: '',
  docs: '',
  lessons: ''
})

const stateOptions = [
  'Planning',
  'Development',
  'Testing',
  'Release',
  'Completed',
  'Cancelled'
]

const riskStateOptions = [
  'Low',
  'Medium',
  'High',
  'Critical'
]

watch(() => props.release, (newRelease) => {
  if (newRelease) {
    formData.value = {
      name: newRelease.name || '',
      state: newRelease.state || '',
      prd_signoff: formatDateForInput(newRelease.prd_signoff) || '',
      prd_link: newRelease.prd_link || '',
      test_strategy_signoff: formatDateForInput(newRelease.test_strategy_signoff) || '',
      test_strategy_link: newRelease.test_strategy_link || '',
      release_branch_off: formatDateForInput(newRelease.release_branch_off) || '',
      release_code_freeze: formatDateForInput(newRelease.release_code_freeze) || '',
      risk_state: newRelease.risk_state || '',
      test_report: newRelease.test_report || '',
      risk: newRelease.risk || '',
      softwares: newRelease.softwares || '',
      docs: newRelease.docs || '',
      lessons: newRelease.lessons || ''
    }
  } else {
    resetForm()
  }
}, { immediate: true })

function resetForm() {
  formData.value = {
    name: '',
    state: '',
    prd_signoff: '',
    prd_link: '',
    test_strategy_signoff: '',
    test_strategy_link: '',
    release_branch_off: '',
    release_code_freeze: '',
    risk_state: '',
    test_report: '',
    risk: '',
    softwares: '',
    docs: '',
    lessons: ''
  }
}

function formatDateForInput(dateString) {
  if (!dateString) return ''
  return new Date(dateString).toISOString().split('T')[0]
}

async function save() {
  const isValid = await form.value?.validate()
  if (isValid?.valid) {
    const data = { ...formData.value }
    
    // Convert empty date strings to null
    const dateFields = ['prd_signoff', 'test_strategy_signoff', 'release_branch_off', 'release_code_freeze']
    dateFields.forEach(field => {
      if (data[field] === '') {
        data[field] = null
      }
    })
    
    emit('save', data)
  }
}
</script> 