<template>
  <v-dialog v-model="dialog" max-width="800px" persistent>
    <v-card>
      <v-card-title class="text-h5">{{ isEdit ? 'Edit Risk' : 'Add New Risk' }}</v-card-title>

      <v-card-text>
        <v-container>
          <v-row>
            <v-col cols="12" sm="6">
              <v-text-field
                v-model="formData.team"
                label="Team"
                required
                :error-messages="errors.team"
              ></v-text-field>
            </v-col>

            <v-col cols="12" sm="6">
              <v-select
                v-model="formData.type"
                label="Type"
                :items="typeOptions"
                required
                :error-messages="errors.type"
              ></v-select>
            </v-col>

            <v-col cols="12">
              <v-textarea
                v-model="formData.description"
                label="Description"
                rows="3"
                required
                :error-messages="errors.description"
              ></v-textarea>
            </v-col>

            <v-col cols="12" sm="6">
              <v-select
                v-model="formData.probability"
                label="Probability"
                :items="probabilityOptions"
                required
                :error-messages="errors.probability"
              ></v-select>
            </v-col>

            <v-col cols="12" sm="6">
              <v-select
                v-model="formData.impact"
                label="Impact"
                :items="impactOptions"
                required
                :error-messages="errors.impact"
              ></v-select>
            </v-col>

            <v-col cols="12">
              <v-select
                v-model="formData.status"
                label="Status"
                :items="statusOptions"
                required
                :error-messages="errors.status"
              ></v-select>
            </v-col>

            <v-col cols="12">
              <v-textarea
                v-model="formData.migration"
                label="Migration"
                rows="2"
                :error-messages="errors.migration"
              ></v-textarea>
            </v-col>

            <v-col cols="12">
              <v-textarea
                v-model="formData.fallback_strategy"
                label="Fallback Strategy"
                rows="2"
                :error-messages="errors.fallback_strategy"
              ></v-textarea>
            </v-col>

            <v-col cols="12" sm="6">
              <v-text-field
                v-model="formData.keeper"
                label="Keeper"
                :error-messages="errors.keeper"
              ></v-text-field>
            </v-col>

            <v-col cols="12">
              <v-textarea
                v-model="formData.comments"
                label="Comments"
                rows="2"
                :error-messages="errors.comments"
              ></v-textarea>
            </v-col>
          </v-row>
        </v-container>
      </v-card-text>

      <v-card-actions>
        <v-spacer></v-spacer>
        <v-btn text @click="closeDialog">Cancel</v-btn>
        <v-btn color="primary" @click="saveData" :loading="loading">
          {{ isEdit ? 'Update' : 'Create' }}
        </v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<script setup>
import { ref, computed, watch } from 'vue'

const props = defineProps({
  modelValue: Boolean,
  risk: Object,
  loading: Boolean,
})

const emit = defineEmits(['update:modelValue', 'save'])

const dialog = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value),
})

const isEdit = computed(() => !!props.risk?.id)

const formData = ref({
  team: '',
  type: '',
  description: '',
  probability: '',
  impact: '',
  status: '',
  migration: '',
  fallback_strategy: '',
  keeper: '',
  comments: '',
})

const errors = ref({
  team: [],
  type: [],
  description: [],
  probability: [],
  impact: [],
  status: [],
  migration: [],
  fallback_strategy: [],
  keeper: [],
  comments: [],
})

const typeOptions = [
  'Technical',
  'Business',
  'Security',
  'Performance',
  'Integration',
  'Dependency',
  'Resource',
  'Regulatory',
]

const probabilityOptions = ['Low', 'Medium', 'High']

const impactOptions = ['Low', 'Medium', 'High']

const statusOptions = ['Open', 'In Progress', 'Mitigated', 'Closed', 'Accepted']

watch(
  () => props.risk,
  (newRisk) => {
    if (newRisk) {
      formData.value = {
        team: newRisk.team || '',
        type: newRisk.type || '',
        description: newRisk.description || '',
        probability: newRisk.probability || '',
        impact: newRisk.impact || '',
        status: newRisk.status || '',
        migration: newRisk.migration || '',
        fallback_strategy: newRisk.fallback_strategy || '',
        keeper: newRisk.keeper || '',
        comments: newRisk.comments || '',
      }
    } else {
      resetForm()
    }
  },
  { immediate: true }
)

function resetForm() {
  formData.value = {
    team: '',
    type: '',
    description: '',
    probability: '',
    impact: '',
    status: '',
    migration: '',
    fallback_strategy: '',
    keeper: '',
    comments: '',
  }
  errors.value = {
    team: [],
    type: [],
    description: [],
    probability: [],
    impact: [],
    status: [],
    migration: [],
    fallback_strategy: [],
    keeper: [],
    comments: [],
  }
}

function validateForm() {
  errors.value = {
    team: [],
    type: [],
    description: [],
    probability: [],
    impact: [],
    status: [],
    migration: [],
    fallback_strategy: [],
    keeper: [],
    comments: [],
  }

  let isValid = true

  if (!formData.value.team) {
    errors.value.team.push('Team is required')
    isValid = false
  }

  if (!formData.value.type) {
    errors.value.type.push('Type is required')
    isValid = false
  }

  if (!formData.value.description) {
    errors.value.description.push('Description is required')
    isValid = false
  }

  if (!formData.value.probability) {
    errors.value.probability.push('Probability is required')
    isValid = false
  }

  if (!formData.value.impact) {
    errors.value.impact.push('Impact is required')
    isValid = false
  }

  if (!formData.value.status) {
    errors.value.status.push('Status is required')
    isValid = false
  }

  return isValid
}

function saveData() {
  if (validateForm()) {
    emit('save', { ...formData.value })
  }
}

function closeDialog() {
  dialog.value = false
  resetForm()
}
</script>
