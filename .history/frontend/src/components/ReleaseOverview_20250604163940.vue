<template>
  <v-container>
    <v-row>
      <v-col cols="12" md="6">
        <v-card class="mb-4">
          <v-card-title>Basic Information</v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item>
                <v-list-item-title>Name</v-list-item-title>
                <v-list-item-subtitle>{{ release.name }}</v-list-item-subtitle>
              </v-list-item>
              <v-list-item>
                <v-list-item-title>State</v-list-item-title>
                <v-list-item-subtitle>{{ release.state || 'Not set' }}</v-list-item-subtitle>
              </v-list-item>
              <v-list-item>
                <v-list-item-title>Risk State</v-list-item-title>
                <v-list-item-subtitle>{{ release.risk_state || 'Not set' }}</v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="6">
        <v-card class="mb-4">
          <v-card-title>Timeline</v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item>
                <v-list-item-title>PRD Signoff</v-list-item-title>
                <v-list-item-subtitle>{{ formatDate(release.prd_signoff) || 'Not set' }}</v-list-item-subtitle>
              </v-list-item>
              <v-list-item>
                <v-list-item-title>Test Strategy Signoff</v-list-item-title>
                <v-list-item-subtitle>{{ formatDate(release.test_strategy_signoff) || 'Not set' }}</v-list-item-subtitle>
              </v-list-item>
              <v-list-item>
                <v-list-item-title>Release Branch Off</v-list-item-title>
                <v-list-item-subtitle>{{ formatDate(release.release_branch_off) || 'Not set' }}</v-list-item-subtitle>
              </v-list-item>
              <v-list-item>
                <v-list-item-title>Release Code Freeze</v-list-item-title>
                <v-list-item-subtitle>{{ formatDate(release.release_code_freeze) || 'Not set' }}</v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    
    <v-row>
      <v-col cols="12" md="6">
        <v-card class="mb-4">
          <v-card-title>Links</v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item v-if="release.prd_link">
                <v-list-item-title>PRD Link</v-list-item-title>
                <v-list-item-subtitle>
                  <a :href="release.prd_link" target="_blank" class="text-primary">{{ release.prd_link }}</a>
                </v-list-item-subtitle>
              </v-list-item>
              <v-list-item v-if="release.test_strategy_link">
                <v-list-item-title>Test Strategy Link</v-list-item-title>
                <v-list-item-subtitle>
                  <a :href="release.test_strategy_link" target="_blank" class="text-primary">{{ release.test_strategy_link }}</a>
                </v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
      
      <v-col cols="12" md="6">
        <v-card class="mb-4">
          <v-card-title>Additional Information</v-card-title>
          <v-card-text>
            <v-list>
              <v-list-item v-if="release.softwares">
                <v-list-item-title>Softwares</v-list-item-title>
                <v-list-item-subtitle>{{ release.softwares }}</v-list-item-subtitle>
              </v-list-item>
              <v-list-item v-if="release.docs">
                <v-list-item-title>Documentation</v-list-item-title>
                <v-list-item-subtitle>{{ release.docs }}</v-list-item-subtitle>
              </v-list-item>
            </v-list>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    
    <v-row>
      <v-col cols="12">
        <v-card v-if="release.test_report" class="mb-4">
          <v-card-title>Test Report</v-card-title>
          <v-card-text>
            <p>{{ release.test_report }}</p>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    
    <v-row>
      <v-col cols="12">
        <v-card v-if="release.risk" class="mb-4">
          <v-card-title>Risk</v-card-title>
          <v-card-text>
            <p>{{ release.risk }}</p>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
    
    <v-row>
      <v-col cols="12">
        <v-card v-if="release.lessons" class="mb-4">
          <v-card-title>Lessons Learned</v-card-title>
          <v-card-text>
            <p>{{ release.lessons }}</p>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>
  </v-container>
</template>

<script setup>
import { defineProps } from 'vue'

defineProps({
  release: {
    type: Object,
    required: true
  }
})

function formatDate(dateString) {
  if (!dateString) return null
  return new Date(dateString).toLocaleDateString()
}
</script> 