<template>
  <div>
    <div class="d-flex justify-space-between align-center mb-4">
      <div>
        <h3>Feature Summary</h3>
        <div class="mt-2">
          <v-chip-group>
            <v-chip
              v-for="stat in getFeatureStats()"
              :key="stat.state"
              :color="stat.color"
              size="small"
              variant="flat"
              class="text-white mr-2"
            >
              {{ stat.state }}: {{ stat.count }}
            </v-chip>
          </v-chip-group>
        </div>
      </div>
      <v-btn color="primary" @click="showCreateDialog = true">
        <v-icon>mdi-plus</v-icon>
        Add Feature
      </v-btn>
    </div>

    <v-data-table :headers="headers" :items="features" class="elevation-1">
      <template v-slot:item.description="{ item }">
        <div
          style="max-width: 200px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis"
        >
          {{ item.description || '-' }}
        </div>
      </template>

      <template v-slot:item.started="{ item }">
        {{ item.started ? new Date(item.started).toLocaleDateString() : '-' }}
      </template>

      <template v-slot:item.dev_done="{ item }">
        {{ item.dev_done ? new Date(item.dev_done).toLocaleDateString() : '-' }}
      </template>

      <template v-slot:item.ended="{ item }">
        {{ item.ended ? new Date(item.ended).toLocaleDateString() : '-' }}
      </template>

      <template v-slot:item.state="{ item }">
        <v-chip :color="getStateColor(item.state)" size="small" variant="flat" class="text-white">
          {{ item.state || '-' }}
        </v-chip>
      </template>

      <template v-slot:item.actions="{ item }">
        <v-btn size="small" icon @click="editFeature(item)">
          <v-icon>mdi-pencil</v-icon>
        </v-btn>
        <v-btn size="small" icon @click="deleteFeature(item)">
          <v-icon>mdi-delete</v-icon>
        </v-btn>
      </template>
    </v-data-table>

    <!-- Feature Dialog -->
    <v-dialog v-model="showCreateDialog" max-width="800">
      <v-card>
        <v-card-title>{{ editingFeature?.id ? 'Edit Feature' : 'Add Feature' }}</v-card-title>
        <v-card-text>
          <v-form ref="form">
            <v-row>
              <v-col cols="12" md="6">
                <v-text-field v-model="formData.name" label="Name" required></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field v-model="formData.owner" label="Owner"></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12">
                <v-textarea
                  v-model="formData.description"
                  label="Description"
                  rows="3"
                ></v-textarea>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-select v-model="formData.state" label="State" :items="stateOptions"></v-select>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.started"
                  label="Started Date"
                  type="date"
                  hint="When development started"
                ></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.dev_done"
                  label="Dev Done Date"
                  type="date"
                  hint="When development completed"
                ></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.ended"
                  label="End Date"
                  type="date"
                  hint="When feature was fully completed"
                ></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12" md="6">
                <v-text-field v-model="formData.design_spec" label="Design Spec URL"></v-text-field>
              </v-col>
              <v-col cols="12" md="6">
                <v-text-field
                  v-model="formData.test_strategy"
                  label="Test Strategy URL"
                ></v-text-field>
              </v-col>
            </v-row>

            <v-row>
              <v-col cols="12">
                <v-text-field v-model="formData.test_report" label="Test Report URL"></v-text-field>
              </v-col>
            </v-row>
          </v-form>
        </v-card-text>
        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn @click="closeDialog">Cancel</v-btn>
          <v-btn color="primary" @click="saveFeature">Save</v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<script setup>
import { ref, defineProps } from 'vue'
import { useReleaseStore } from '@/stores/release'

const props = defineProps({
  releaseId: Number,
  features: Array,
})

const releaseStore = useReleaseStore()
const showCreateDialog = ref(false)
const editingFeature = ref(null)
const formData = ref({
  name: '',
  description: '',
  owner: '',
  state: '',
  started: '',
  dev_done: '',
  ended: '',
  design_spec: '',
  test_strategy: '',
  test_report: '',
})

const headers = [
  { title: 'Name', key: 'name' },
  { title: 'Description', key: 'description' },
  { title: 'Owner', key: 'owner' },
  { title: 'State', key: 'state' },
  { title: 'Started', key: 'started' },
  { title: 'Dev Done', key: 'dev_done' },
  { title: 'Ended', key: 'ended' },
  { title: 'Actions', key: 'actions', sortable: false },
]

// JIRA-style state options
const stateOptions = [
  'To Do',
  'In Progress',
  'Code Review',
  'Testing',
  'QA Review',
  'Ready for Release',
  'Done',
  'Blocked',
  'Cancelled',
]

// Get state color based on JIRA conventions
function getStateColor(state) {
  const stateColors = {
    'To Do': 'grey',
    'In Progress': 'blue',
    'Code Review': 'purple',
    Testing: 'orange',
    'QA Review': 'amber',
    'Ready for Release': 'teal',
    Done: 'green',
    Blocked: 'red',
    Cancelled: 'deep-grey',
  }
  return stateColors[state] || 'grey'
}

// Get feature statistics by state
function getFeatureStats() {
  const stats = {}
  stateOptions.forEach((state) => {
    stats[state] = 0
  })

  props.features?.forEach((feature) => {
    if (feature.state && Object.prototype.hasOwnProperty.call(stats, feature.state)) {
      stats[feature.state]++
    }
  })

  return stateOptions
    .map((state) => ({
      state,
      count: stats[state],
      color: getStateColor(state),
    }))
    .filter((stat) => stat.count > 0) // Only show states that have features
}

function editFeature(feature) {
  editingFeature.value = feature
  formData.value = {
    ...feature,
    started: feature.started ? new Date(feature.started).toISOString().split('T')[0] : '',
    dev_done: feature.dev_done ? new Date(feature.dev_done).toISOString().split('T')[0] : '',
    ended: feature.ended ? new Date(feature.ended).toISOString().split('T')[0] : '',
  }
  showCreateDialog.value = true
}

function deleteFeature(feature) {
  // Implementation for delete
}

function resetForm() {
  formData.value = {
    name: '',
    description: '',
    owner: '',
    state: '',
    started: '',
    dev_done: '',
    ended: '',
    design_spec: '',
    test_strategy: '',
    test_report: '',
  }
}

function closeDialog() {
  showCreateDialog.value = false
  editingFeature.value = null
  resetForm()
}

async function saveFeature() {
  if (editingFeature.value?.id) {
    await releaseStore.updateFeature(editingFeature.value.id, formData.value)
  } else {
    await releaseStore.createFeature(props.releaseId, formData.value)
  }
  closeDialog()
}
</script>
