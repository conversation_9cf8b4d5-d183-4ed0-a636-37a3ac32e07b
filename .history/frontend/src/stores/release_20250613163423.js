import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import axios from 'axios'

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:9001'

export const useReleaseStore = defineStore('release', () => {
  const releases = ref([])
  const currentRelease = ref(null)
  const loading = ref(false)
  const error = ref(null)

  const api = axios.create({
    baseURL: `${API_BASE_URL}/api`,
    headers: {
      'Content-Type': 'application/json',
    },
  })

  // Getters
  const getReleaseById = computed(() => {
    return (id) => releases.value.find((release) => release.id === id)
  })

  // Actions
  async function fetchReleases() {
    loading.value = true
    error.value = null
    try {
      const response = await api.get('/releases')
      releases.value = response.data || []
    } catch (err) {
      error.value = err.message
      console.error('Error fetching releases:', err)
    } finally {
      loading.value = false
    }
  }

  async function fetchRelease(id) {
    loading.value = true
    error.value = null
    try {
      const response = await api.get(`/releases/${id}`)
      currentRelease.value = response.data
      return response.data
    } catch (err) {
      error.value = err.message
      console.error('Error fetching release:', err)
    } finally {
      loading.value = false
    }
  }

  async function createRelease(releaseData) {
    loading.value = true
    error.value = null
    try {
      const response = await api.post('/releases', releaseData)
      releases.value.push(response.data)
      return response.data
    } catch (err) {
      error.value = err.message
      console.error('Error creating release:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function updateRelease(id, releaseData) {
    loading.value = true
    error.value = null
    try {
      const response = await api.put(`/releases/${id}`, releaseData)
      const index = releases.value.findIndex((r) => r.id === id)
      if (index !== -1) {
        releases.value[index] = response.data
      }
      if (currentRelease.value && currentRelease.value.id === id) {
        currentRelease.value = response.data
      }
      return response.data
    } catch (err) {
      error.value = err.message
      console.error('Error updating release:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function deleteRelease(id) {
    loading.value = true
    error.value = null
    try {
      await api.delete(`/releases/${id}`)
      releases.value = releases.value.filter((r) => r.id !== id)
      if (currentRelease.value && currentRelease.value.id === id) {
        currentRelease.value = null
      }
    } catch (err) {
      error.value = err.message
      console.error('Error deleting release:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function createFeature(releaseId, featureData) {
    loading.value = true
    error.value = null
    try {
      const response = await api.post(`/releases/${releaseId}/features`, featureData)
      if (currentRelease.value && currentRelease.value.id === releaseId) {
        if (!currentRelease.value.feature_summaries) {
          currentRelease.value.feature_summaries = []
        }
        currentRelease.value.feature_summaries.push(response.data)
      }
      return response.data
    } catch (err) {
      error.value = err.message
      console.error('Error creating feature:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function updateFeature(id, featureData) {
    loading.value = true
    error.value = null
    try {
      const response = await api.put(`/features/${id}`, featureData)
      if (currentRelease.value && currentRelease.value.feature_summaries) {
        const index = currentRelease.value.feature_summaries.findIndex((f) => f.id === id)
        if (index !== -1) {
          currentRelease.value.feature_summaries[index] = response.data
        }
      }
      return response.data
    } catch (err) {
      error.value = err.message
      console.error('Error updating feature:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function deleteFeature(id) {
    loading.value = true
    error.value = null
    try {
      await api.delete(`/features/${id}`)
      if (currentRelease.value && currentRelease.value.feature_summaries) {
        currentRelease.value.feature_summaries = currentRelease.value.feature_summaries.filter(
          (f) => f.id !== id
        )
      }
    } catch (err) {
      error.value = err.message
      console.error('Error deleting feature:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  // 质量图片相关方法
  async function uploadQualityImage(releaseId, formData) {
    loading.value = true
    error.value = null
    try {
      const response = await api.post(`/releases/${releaseId}/qualities`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      })
      if (currentRelease.value && currentRelease.value.id === releaseId) {
        if (!currentRelease.value.qualities) {
          currentRelease.value.qualities = []
        }
        currentRelease.value.qualities.push(response.data)
      }
      return response.data
    } catch (err) {
      error.value = err.message
      console.error('Error uploading quality image:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  async function deleteQualityImage(id) {
    loading.value = true
    error.value = null
    try {
      await api.delete(`/qualities/${id}`)
      if (currentRelease.value && currentRelease.value.qualities) {
        currentRelease.value.qualities = currentRelease.value.qualities.filter((q) => q.id !== id)
      }
    } catch (err) {
      error.value = err.message
      console.error('Error deleting quality image:', err)
      throw err
    } finally {
      loading.value = false
    }
  }

  function setCurrentRelease(release) {
    currentRelease.value = release
  }

  function clearCurrentRelease() {
    currentRelease.value = null
  }

  return {
    releases,
    currentRelease,
    loading,
    error,
    getReleaseById,
    fetchReleases,
    fetchRelease,
    createRelease,
    updateRelease,
    deleteRelease,
    createFeature,
    updateFeature,
    deleteFeature,
    uploadQualityImage,
    deleteQualityImage,
    setCurrentRelease,
    clearCurrentRelease,
  }
})
