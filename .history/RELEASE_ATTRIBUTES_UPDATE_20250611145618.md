# Release 属性分组更新说明

## 概览

根据您的需求，我已经将 Release 的属性按照 MT0-MT4 阶段进行了分组，并更新了前端显示以支持分组展示。

## 分组结构

### Release Name (独立)

- `name` - Release 名称（必填字段）

### MT0 - PRD 阶段

- `prd_signoff` - PRD Signoff 日期
- `prd_link` - PRD 链接

### MT1 - 测试策略阶段

- `test_strategy_signoff` - 测试策略 Signoff 日期
- `test_strategy_link` - 测试策略链接
- `release_branch_off` - Release 分支创建日期

### MT2 - 代码冻结阶段

- `release_code_freeze` - 代码冻结日期

### MT3 - 发布阶段

- `state` - Release 状态
- `test_report` - 测试报告
- `risk_link` - 风险评估链接（新增）
- `risk_state` - 风险状态
- `software_download` - 软件下载链接（新增）
- `doc_link` - 文档链接（新增）

### MT4 - 总结阶段

- `lessons` - 经验总结

## 主要更改

### 1. 后端模型更新 (`backend/models/models.go`)

- 重新组织了 Release 结构体的字段
- 添加了新字段：
  - `risk_link` - 风险评估链接
  - `software_download` - 软件下载链接
  - `doc_link` - 文档链接
- 保留了旧字段以确保向后兼容性：
  - `risk` (deprecated)
  - `softwares` (deprecated)  
  - `docs` (deprecated)
- 添加了注释说明每个字段属于哪个 MT 阶段

### 2. 前端对话框更新 (`frontend/src/components/ReleaseDialog.vue`)

- 使用可展开面板按 MT0-MT4 分组显示字段
- 每个分组有不同颜色的标签：
  - MT0: 蓝色 (primary)
  - MT1: 紫色 (secondary)
  - MT2: 橙色 (warning)
  - MT3: 绿色 (success)
  - MT4: 天蓝色 (info)
- 支持新旧字段的兼容性映射
- 增大了对话框宽度以适应分组布局

### 3. 概览页面更新 (`frontend/src/components/ReleaseOverview.vue`)

- 按 MT 阶段分组显示 release 信息
- 添加了发布时间轴，显示各个关键时间点
- 支持链接字段的点击跳转
- 改进了视觉设计，使用了分组卡片和图标

### 4. 列表页面更新 (`frontend/src/components/ReleaseSummary.vue`)

- 更新了表格列定义，按 MT 阶段分组
- 添加了新字段的列：
  - Risk Link
  - Software DL (Download)
  - Doc Link
- 保留了旧字段列以确保兼容性
- 支持链接字段的图标按钮点击

## 兼容性说明

- 所有更改都保持了向后兼容性
- 旧字段仍然存在于数据库中，标记为 deprecated
- 前端会优先显示新字段，如果新字段为空则回退到旧字段
- 数据库会自动迁移添加新字段

## 使用说明

1. 重启后端服务以应用数据库迁移
2. 创建或编辑 Release 时，字段会按 MT 阶段分组显示
3. 在 Release 概览页面可以看到按阶段组织的信息
4. 列表页面提供了完整的字段视图，支持新旧字段

## 技术特性

- 响应式设计，支持移动端
- 可展开/折叠的分组面板
- 链接字段支持一键跳转
- 时间轴视图显示发布进度
- 颜色编码的状态和风险指示器

## 下一步建议

1. 考虑添加字段验证规则
2. 可以增加字段间的依赖关系检查
3. 考虑添加模板功能以快速创建标准化的 Release
4. 可以添加字段的历史变更记录
