# Release Management System

一个用于管理研发发布过程的系统，为项目经理提供全面的发布管理功能。

## 功能特性

### 🚀 Release 管理

- 创建和管理 Release
- 跟踪发布状态和时间线
- PRD 和测试策略管理
- 风险评估和缓解策略

### 📋 Feature Summary

- 功能需求跟踪
- 开发进度管理
- 负责人分配
- 设计规范和测试报告

### 🔄 CCB Summary (Change Control Board)

- 变更请求管理
- 审批流程跟踪
- 创建者和状态管理

### ⚠️ Risk Management

- 风险识别和评估
- 概率和影响分析
- 缓解策略制定
- 风险状态跟踪

### 📊 Quality Assurance

- 质量报告图片管理
- 测试结果展示

## 技术架构

### 前端

- **Vue 3** - 现代化的前端框架
- **Vite** - 快速的构建工具
- **Pinia** - 状态管理
- **Vuetify 3** - Material Design UI 组件库
- **Axios** - HTTP 客户端
- **ESLint + Prettier** - 代码规范和格式化

### 后端

- **Go** - 高性能后端语言
- **Gin** - Web 框架
- **GORM** - ORM 库
- **PostgreSQL** - 数据库
- **Docker** - 容器化部署

## 快速开始

### 使用启动脚本（推荐）

1. 克隆项目

```bash
git clone <repository-url>
cd release-management
```

2. 配置环境变量（可选）

编辑 `.env` 文件来自定义配置：

```bash
nano .env
```

主要配置项：

- `HOST`: 服务器IP地址（默认：***************）
- `FRONTEND_PORT`: 前端端口（默认：3000）
- `BACKEND_PORT`: 后端端口（默认：8080）

3. 启动所有服务

```bash
./start-services.sh
```

3. 访问应用

- 前端: <http://***************:3000>
- 后端 API: <http://***************:8080/api>

### 使用 Docker Compose

```bash
docker-compose up -d
```

访问应用：

- 前端: <http://localhost:3000>
- 后端 API: <http://localhost:8080>

### 本地开发

#### 后端开发

```bash
cd backend
go mod tidy
go run main.go
```

#### 前端开发

```bash
cd frontend
pnpm install
pnpm dev
```

#### 数据库

确保 PostgreSQL 运行在 localhost:5432，数据库名为 `release_management`

## 项目结构

```
release-management/
├── backend/                 # Go 后端
│   ├── controllers/        # 控制器
│   ├── models/            # 数据模型
│   ├── main.go           # 主程序
│   ├── go.mod            # Go 依赖
│   └── Dockerfile        # 后端 Docker 配置
├── frontend/               # Vue 前端
│   ├── src/
│   │   ├── components/   # Vue 组件
│   │   ├── stores/       # Pinia 状态管理
│   │   ├── views/        # 页面视图
│   │   └── main.js       # 入口文件
│   ├── package.json      # 前端依赖
│   └── Dockerfile        # 前端 Docker 配置
└── docker-compose.yml     # Docker Compose 配置
```

## API 文档

### Release 相关接口

- `GET /api/releases` - 获取所有 Release
- `GET /api/releases/:id` - 获取单个 Release
- `POST /api/releases` - 创建 Release
- `PUT /api/releases/:id` - 更新 Release
- `DELETE /api/releases/:id` - 删除 Release

### Feature 相关接口

- `GET /api/releases/:releaseId/features` - 获取 Release 的所有 Feature
- `POST /api/releases/:releaseId/features` - 创建 Feature
- `PUT /api/features/:id` - 更新 Feature
- `DELETE /api/features/:id` - 删除 Feature

## 开发指南

### 代码规范

- 前端使用 ESLint + Prettier 进行代码格式化
- 后端使用 go fmt 和 golint 进行代码规范检查

### 提交规范

请使用有意义的提交信息，遵循常见的提交规范。

## 贡献

欢迎提交 Issue 和 Pull Request！

## 许可证

MIT License
