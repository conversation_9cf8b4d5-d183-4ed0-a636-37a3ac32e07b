# 表格布局对齐和边框朴素化更新

## 更新内容

### 1. 表格和基本信息栏左右对齐

- **容器对齐**: 为Summary Cards添加了专用容器 `.summary-cards-container`
- **滚动条补偿**: 为Summary Cards容器添加8px右边距，与表格滚动条宽度对齐
- **布局统一**: 确保上方信息栏和下方表格的左右边界完全对齐

### 2. 表格边框朴素化

- **移除彩色边框**: 取消了MT分组的彩色左边框，改为统一的朴素灰色边框
- **统一表头样式**: 所有表头（MT0-MT4和基本信息列）使用统一的浅灰色背景
- **简洁分隔线**: 分组分隔线改为2px的朴素灰色边框(`#bdbdbd`)

### 3. 视觉风格优化

- **表头背景**: 统一使用 `#f5f5f5` 浅灰色背景
- **文字颜色**: 表头文字使用 `#424242` 深灰色，提供良好的对比度
- **边框颜色**: 统一使用 `#ddd` 作为边框颜色
- **整体风格**: 采用简洁、商务的设计风格

## 技术实现

### CSS布局对齐

```css
/* Summary Cards 容器对齐 */
.summary-cards-container {
  width: 100%;
  overflow-x: auto;
  padding-right: 8px; /* 与滚动条宽度对齐 */
}
```

### 统一表头样式

```css
/* MT分组表头统一样式 */
.grouped-table :deep(.v-data-table__th:has-text('MT0')),
.grouped-table :deep(.v-data-table__th:has-text('MT1')),
.grouped-table :deep(.v-data-table__th:has-text('MT2')),
.grouped-table :deep(.v-data-table__th:has-text('MT3')),
.grouped-table :deep(.v-data-table__th:has-text('MT4')) {
  background-color: #f5f5f5 !important;
  color: #424242 !important;
  font-size: 0.9rem !important;
  font-weight: 700 !important;
  border: 1px solid #ddd !important;
}
```

### 朴素分隔线

```css
/* MT分组间的统一分隔线 */
.grouped-table :deep(.v-data-table__td:nth-child(2)),
.grouped-table :deep(.v-data-table__td:nth-child(4)),
.grouped-table :deep(.v-data-table__td:nth-child(7)),
.grouped-table :deep(.v-data-table__td:nth-child(8)),
.grouped-table :deep(.v-data-table__td:nth-child(14)),
.grouped-table :deep(.v-data-table__td:nth-child(15)) {
  border-left: 2px solid #bdbdbd !important;
}
```

## 设计理念变更

### 从彩色到朴素

- **之前**: 使用蓝色、紫色、橙色、绿色等鲜艳颜色区分MT分组
- **现在**: 使用统一的灰色系，通过边框和布局区分分组
- **优势**: 更专业的商务外观，减少视觉干扰，便于长时间查看

### 布局精准对齐

- **问题解决**: 修复了表格滚动容器导致的左右边界不对齐问题
- **视觉统一**: 确保页面各部分的视觉边界完全一致
- **用户体验**: 提供更整洁、有序的界面布局

## 更新效果

✅ **视觉对齐**: Summary Cards和表格左右边界完美对齐  
✅ **朴素边框**: 统一使用灰色系边框，风格简洁专业  
✅ **易于阅读**: 减少色彩干扰，提高数据可读性  
✅ **商务风格**: 更适合企业级应用的视觉标准  
✅ **布局整洁**: 消除了滚动条导致的布局不对齐问题
