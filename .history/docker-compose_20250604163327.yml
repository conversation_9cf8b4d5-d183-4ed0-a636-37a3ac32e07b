version: '3.8'

services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: release_management
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - release-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "8080:8080"
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: postgres
      DB_PASSWORD: postgres
      DB_NAME: release_management
      GIN_MODE: release
    depends_on:
      - postgres
    networks:
      - release-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "3000:3000"
    environment:
      VITE_API_BASE_URL: http://localhost:8080
    depends_on:
      - backend
    networks:
      - release-network

volumes:
  postgres_data:


networks:
  release-network:
    driver: bridge
