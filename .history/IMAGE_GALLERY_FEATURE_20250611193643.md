# 图片管理功能文档

## 功能概述

新增的图片管理功能为Release Management系统提供了本地图片存储和管理能力，支持多种图片格式的上传、预览、组织和管理。

## 主要特性

### 1. 本地存储

- **存储方式**: 使用浏览器的localStorage进行本地存储
- **存储限制**: 总大小限制为5MB
- **数据持久化**: 图片数据和配置信息会自动保存到本地
- **兼容性**: 支持所有现代浏览器

### 2. 文件上传

- **支持格式**: JPG, PNG, GIF, WebP
- **上传方式**:
  - 文件选择器选择
  - 拖拽上传
- **批量上传**: 支持同时选择多个文件
- **智能限制**: 自动检查存储空间，防止超出5MB限制
- **文件验证**: 自动验证文件类型，只接受图片文件

### 3. 网格布局显示

- **响应式网格**: 使用CSS Grid布局，自适应容器宽度
- **多种网格大小**:
  - 小 (150px)
  - 中 (200px) - 默认
  - 大 (250px)
  - 特大 (300px)
- **动态调整**: 实时切换网格大小，重新排列图片

### 4. 可拖拽调整大小

- **动态调整**: 开启"允许调整大小"开关后，每张图片可独立调整尺寸
- **鼠标拖拽**: 通过图片右下角的调整手柄进行拖拽
- **保持比例**: 支持自由调整宽高，不强制保持比例
- **最小尺寸**: 最小尺寸限制为100x100像素
- **实时保存**: 调整后的尺寸会自动保存到本地存储

### 5. 图片管理

- **图片信息**: 显示文件名、文件大小
- **批量选择**: 支持单选、多选、全选操作
- **批量删除**: 可以批量删除选中的图片
- **单个删除**: 每张图片的单独删除操作
- **清空功能**: 一键清空所有图片

### 6. 图片预览

- **全屏预览**: 点击预览按钮打开全屏预览对话框
- **高清显示**: 预览时以原始尺寸显示图片
- **详细信息**: 显示文件大小、原始尺寸等详细信息
- **缩放适配**: 图片自动适配对话框大小，最大高度80vh

### 7. 存储管理

- **实时监控**: 实时显示当前存储使用情况
- **可视化指示**: 使用颜色编码的芯片显示存储状态
  - 绿色: 使用率 < 60%
  - 橙色: 使用率 60-80%
  - 红色: 使用率 > 80%
- **自动验证**: 加载时自动验证存储数据的完整性

## 界面布局

### 1. 标题栏

- 功能标题：图片管理
- 存储状态显示：当前使用量/总限制
- 清空所有按钮

### 2. 上传区域

- 拖拽上传区域（虚线边框）
- 文件选择器
- 支持格式说明

### 3. 工具栏

- 网格大小选择器
- 调整大小开关
- 全选按钮
- 批量删除按钮（显示选中数量）

### 4. 图片网格

- 响应式网格布局
- 图片缩略图
- 选择复选框
- 图片信息（文件名、大小）
- 操作按钮（预览、删除）
- 调整大小手柄（可选）

### 5. 空状态

- 无图片时显示占位符
- 引导用户上传图片

## 技术实现

### 前端技术栈

- **Vue 3**: 响应式UI框架
- **Vuetify 3**: 现代化UI组件库
- **CSS Grid**: 响应式网格布局
- **FileReader API**: 文件读取和Base64编码
- **localStorage**: 本地数据存储

### 组件结构

```
ImageGallery.vue
├── 文件上传处理
├── 图片数据管理
├── 本地存储操作
├── 拖拽调整大小
├── 网格布局控制
└── 预览对话框
```

### 数据结构

```javascript
{
  id: number,           // 唯一标识
  name: string,         // 文件名
  src: string,          // Base64数据URL
  size: number,         // 文件大小（字节）
  width: number,        // 显示宽度
  height: number,       // 显示高度
  naturalWidth: number, // 原始宽度
  naturalHeight: number,// 原始高度
  timestamp: string     // 创建时间
}
```

## 使用指南

### 1. 进入图片管理

- 在左侧导航栏点击"图片管理"菜单项
- 图标：多图片图标

### 2. 上传图片

- **方法一**: 点击上传区域的文件选择器，选择图片文件
- **方法二**: 直接将图片文件拖拽到上传区域
- 支持同时选择/拖拽多个图片

### 3. 调整显示设置

- 在工具栏选择合适的网格大小
- 开启"允许调整大小"来启用个别图片的尺寸调整

### 4. 管理图片

- 点击图片或复选框进行选择
- 使用工具栏的"全选"快速选择所有图片
- 点击"删除选中"批量删除图片

### 5. 预览图片

- 点击图片右上角的眼睛图标打开预览
- 在预览对话框中查看原始尺寸的图片

### 6. 调整图片大小

- 开启"允许调整大小"开关
- 鼠标悬停在图片上，在右下角出现调整手柄
- 拖拽手柄调整图片显示大小

### 7. 清理存储

- 点击标题栏的"清空所有"删除所有图片
- 或选中特定图片后使用批量删除

## 性能考虑

### 存储优化

- Base64编码会增加约33%的存储开销
- 5MB限制可存储大约20-50张中等分辨率图片
- 自动压缩和缓存机制确保最佳性能

### 用户体验

- 响应式设计，适配各种屏幕尺寸
- 平滑的动画和过渡效果
- 直观的视觉反馈和状态指示

### 浏览器兼容性

- 支持所有现代浏览器（Chrome、Firefox、Safari、Edge）
- 使用标准Web API，无需额外插件

## 注意事项

1. **存储限制**: 总大小限制为5MB，超出限制时会阻止新的上传
2. **数据持久性**: 数据存储在浏览器本地，清除浏览器数据会丢失图片
3. **格式支持**: 仅支持常见图片格式，不支持视频或其他文件类型
4. **隐私安全**: 所有数据存储在本地，不会上传到服务器

## 未来扩展

可能的功能扩展方向：

- 图片编辑功能（旋转、裁剪、滤镜）
- 文件夹分类管理
- 服务器端存储选项
- 图片标签和搜索功能
- 导出和备份功能
