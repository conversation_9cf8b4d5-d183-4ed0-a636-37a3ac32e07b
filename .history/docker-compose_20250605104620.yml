version: "3.8"

# Load environment variables from .env file
services:
  postgres:
    image: postgres:15
    environment:
      POSTGRES_DB: ${DB_NAME:-release_management}
      POSTGRES_USER: ${DB_USER:-postgres}
      POSTGRES_PASSWORD: ${DB_PASSWORD:-postgres}
    ports:
      - "${DB_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    networks:
      - release-network

  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    ports:
      - "${BACKEND_PORT:-8080}:8080"
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: ${DB_USER:-postgres}
      DB_PASSWORD: ${DB_PASSWORD:-postgres}
      DB_NAME: ${DB_NAME:-release_management}
      SERVER_HOST: ${SERVER_HOST:-0.0.0.0}
      PORT: 8080
      GIN_MODE: ${GIN_MODE:-release}
    depends_on:
      - postgres
    networks:
      - release-network

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    ports:
      - "${FRONTEND_PORT:-3000}:3000"
    environment:
      VITE_API_BASE_URL: http://${SERVER_IP:-localhost}:${BACKEND_PORT:-8080}
    depends_on:
      - backend
    networks:
      - release-network

volumes:
  postgres_data:

networks:
  release-network:
    driver: bridge
