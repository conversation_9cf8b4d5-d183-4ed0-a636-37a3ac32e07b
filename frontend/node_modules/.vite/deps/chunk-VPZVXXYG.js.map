{"version": 3, "sources": ["../../vuetify/src/composables/toggleScope.ts", "../../vuetify/src/composables/proxiedModel.ts", "../../vuetify/src/locale/en.ts", "../../vuetify/src/locale/adapters/vuetify.ts", "../../vuetify/src/composables/locale.ts", "../../vuetify/src/composables/display.ts", "../../vuetify/src/composables/theme.ts"], "sourcesContent": ["// Utilities\nimport { effectScope, onScopeDispose, watch } from 'vue'\n\n// Types\nimport type { EffectScope, WatchSource } from 'vue'\n\nexport function useToggleScope (source: WatchSource<boolean>, fn: (reset: () => void) => void) {\n  let scope: EffectScope | undefined\n  function start () {\n    scope = effectScope()\n    scope.run(() => fn.length\n      ? fn(() => { scope?.stop(); start() })\n      : (fn as any)()\n    )\n  }\n\n  watch(source, active => {\n    if (active && !scope) {\n      start()\n    } else if (!active) {\n      scope?.stop()\n      scope = undefined\n    }\n  }, { immediate: true })\n\n  onScopeDispose(() => {\n    scope?.stop()\n  })\n}\n", "// Composables\nimport { useToggleScope } from '@/composables/toggleScope'\n\n// Utilities\nimport { computed, ref, toRaw, watch } from 'vue'\nimport { getCurrentInstance, toKebabCase } from '@/util'\n\n// Types\nimport type { Ref } from 'vue'\nimport type { EventProp } from '@/util'\n\ntype InnerVal<T> = T extends any[] ? Readonly<T> : T\n\n// Composables\nexport function useProxiedModel<\n  Props extends object & { [key in Prop as `onUpdate:${Prop}`]: EventProp | undefined },\n  Prop extends Extract<keyof Props, string>,\n  Inner = Props[Prop],\n> (\n  props: Props,\n  prop: Prop,\n  defaultValue?: Props[Prop],\n  transformIn: (value?: Props[Prop]) => Inner = (v: any) => v,\n  transformOut: (value: Inner) => Props[Prop] = (v: any) => v,\n) {\n  const vm = getCurrentInstance('useProxiedModel')\n  const internal = ref(props[prop] !== undefined ? props[prop] : defaultValue) as Ref<Props[Prop]>\n  const kebabProp = toKebabCase(prop)\n  const checkKebab = kebabProp !== prop\n\n  const isControlled = checkKebab\n    ? computed(() => {\n      void props[prop]\n      return !!(\n        (vm.vnode.props?.hasOwnProperty(prop) || vm.vnode.props?.hasOwnProperty(kebabProp)) &&\n        (vm.vnode.props?.hasOwnProperty(`onUpdate:${prop}`) || vm.vnode.props?.hasOwnProperty(`onUpdate:${kebabProp}`))\n      )\n    })\n    : computed(() => {\n      void props[prop]\n      return !!(vm.vnode.props?.hasOwnProperty(prop) && vm.vnode.props?.hasOwnProperty(`onUpdate:${prop}`))\n    })\n\n  useToggleScope(() => !isControlled.value, () => {\n    watch(() => props[prop], val => {\n      internal.value = val\n    })\n  })\n\n  const model = computed({\n    get (): any {\n      const externalValue = props[prop]\n      return transformIn(isControlled.value ? externalValue : internal.value)\n    },\n    set (internalValue) {\n      const newValue = transformOut(internalValue)\n      const value = toRaw(isControlled.value ? props[prop] : internal.value)\n      if (value === newValue || transformIn(value) === internalValue) {\n        return\n      }\n      internal.value = newValue\n      vm?.emit(`update:${prop}`, newValue)\n    },\n  }) as any as Ref<InnerVal<Inner>> & { readonly externalValue: Props[Prop] }\n\n  Object.defineProperty(model, 'externalValue', {\n    get: () => isControlled.value ? props[prop] : internal.value,\n  })\n\n  return model\n}\n", "export default {\n  badge: 'Badge',\n  open: 'Open',\n  close: 'Close',\n  dismiss: 'Dismiss',\n  confirmEdit: {\n    ok: 'OK',\n    cancel: 'Cancel',\n  },\n  dataIterator: {\n    noResultsText: 'No matching records found',\n    loadingText: 'Loading items...',\n  },\n  dataTable: {\n    itemsPerPageText: 'Rows per page:',\n    ariaLabel: {\n      sortDescending: 'Sorted descending.',\n      sortAscending: 'Sorted ascending.',\n      sortNone: 'Not sorted.',\n      activateNone: 'Activate to remove sorting.',\n      activateDescending: 'Activate to sort descending.',\n      activateAscending: 'Activate to sort ascending.',\n    },\n    sortBy: 'Sort by',\n  },\n  dataFooter: {\n    itemsPerPageText: 'Items per page:',\n    itemsPerPageAll: 'All',\n    nextPage: 'Next page',\n    prevPage: 'Previous page',\n    firstPage: 'First page',\n    lastPage: 'Last page',\n    pageText: '{0}-{1} of {2}',\n  },\n  dateRangeInput: {\n    divider: 'to',\n  },\n  datePicker: {\n    itemsSelected: '{0} selected',\n    range: {\n      title: 'Select dates',\n      header: 'Enter dates',\n    },\n    title: 'Select date',\n    header: 'Enter date',\n    input: {\n      placeholder: 'Enter date',\n    },\n  },\n  noDataText: 'No data available',\n  carousel: {\n    prev: 'Previous visual',\n    next: 'Next visual',\n    ariaLabel: {\n      delimiter: 'Carousel slide {0} of {1}',\n    },\n  },\n  calendar: {\n    moreEvents: '{0} more',\n    today: 'Today',\n  },\n  input: {\n    clear: 'Clear {0}',\n    prependAction: '{0} prepended action',\n    appendAction: '{0} appended action',\n    otp: 'Please enter OTP character {0}',\n  },\n  fileInput: {\n    counter: '{0} files',\n    counterSize: '{0} files ({1} in total)',\n  },\n  fileUpload: {\n    title: 'Drag and drop files here',\n    divider: 'or',\n    browse: 'Browse Files',\n  },\n  timePicker: {\n    am: 'AM',\n    pm: 'PM',\n    title: 'Select Time',\n  },\n  pagination: {\n    ariaLabel: {\n      root: 'Pagination Navigation',\n      next: 'Next page',\n      previous: 'Previous page',\n      page: 'Go to page {0}',\n      currentPage: 'Page {0}, Current page',\n      first: 'First page',\n      last: 'Last page',\n    },\n  },\n  stepper: {\n    next: 'Next',\n    prev: 'Previous',\n  },\n  rating: {\n    ariaLabel: {\n      item: 'Rating {0} of {1}',\n    },\n  },\n  loading: 'Loading...',\n  infiniteScroll: {\n    loadMore: 'Load more',\n    empty: 'No more',\n  },\n  rules: {\n    required: 'This field is required',\n    email: 'Please enter a valid email',\n    number: 'This field can only contain numbers',\n    integer: 'This field can only contain integer values',\n    capital: 'This field can only contain uppercase letters',\n    maxLength: 'You must enter a maximum of {0} characters',\n    minLength: 'You must enter a minimum of {0} characters',\n    strictLength: 'The length of the entered field is invalid',\n    exclude: 'The {0} character is not allowed',\n    notEmpty: 'Please choose at least one value',\n    pattern: 'Invalid format',\n  },\n}\n", "// Composables\nimport { useProxiedModel } from '@/composables/proxiedModel'\n\n// Utilities\nimport { ref, shallowRef, watch } from 'vue'\nimport { consoleError, consoleWarn, getObjectValueByPath } from '@/util'\n\n// Locales\nimport en from '@/locale/en'\n\n// Types\nimport type { Ref } from 'vue'\nimport type { LocaleInstance, LocaleMessages, LocaleOptions } from '@/composables/locale'\n\nconst LANG_PREFIX = '$vuetify.'\n\nconst replace = (str: string, params: unknown[]) => {\n  return str.replace(/\\{(\\d+)\\}/g, (match: string, index: string) => {\n    return String(params[Number(index)])\n  })\n}\n\nconst createTranslateFunction = (\n  current: Ref<string>,\n  fallback: Ref<string>,\n  messages: Ref<LocaleMessages>,\n) => {\n  return (key: string, ...params: unknown[]) => {\n    if (!key.startsWith(LANG_PREFIX)) {\n      return replace(key, params)\n    }\n\n    const shortKey = key.replace(LANG_PREFIX, '')\n    const currentLocale = current.value && messages.value[current.value]\n    const fallbackLocale = fallback.value && messages.value[fallback.value]\n\n    let str: string = getObjectValueByPath(currentLocale, shortKey, null)\n\n    if (!str) {\n      consoleWarn(`Translation key \"${key}\" not found in \"${current.value}\", trying fallback locale`)\n      str = getObjectValueByPath(fallbackLocale, shortKey, null)\n    }\n\n    if (!str) {\n      consoleError(`Translation key \"${key}\" not found in fallback`)\n      str = key\n    }\n\n    if (typeof str !== 'string') {\n      consoleError(`Translation key \"${key}\" has a non-string value`)\n      str = key\n    }\n\n    return replace(str, params)\n  }\n}\n\nfunction createNumberFunction (current: Ref<string>, fallback: Ref<string>) {\n  return (value: number, options?: Intl.NumberFormatOptions) => {\n    const numberFormat = new Intl.NumberFormat([current.value, fallback.value], options)\n\n    return numberFormat.format(value)\n  }\n}\n\nfunction useProvided <T> (props: any, prop: string, provided: Ref<T>) {\n  const internal = useProxiedModel(props, prop, props[prop] ?? provided.value)\n\n  // TODO: Remove when defaultValue works\n  internal.value = props[prop] ?? provided.value\n\n  watch(provided, v => {\n    if (props[prop] == null) {\n      internal.value = provided.value\n    }\n  })\n\n  return internal as Ref<T>\n}\n\nfunction createProvideFunction (state: { current: Ref<string>, fallback: Ref<string>, messages: Ref<LocaleMessages> }) {\n  return (props: LocaleOptions): LocaleInstance => {\n    const current = useProvided(props, 'locale', state.current)\n    const fallback = useProvided(props, 'fallback', state.fallback)\n    const messages = useProvided(props, 'messages', state.messages)\n\n    return {\n      name: 'vuetify',\n      current,\n      fallback,\n      messages,\n      t: createTranslateFunction(current, fallback, messages),\n      n: createNumberFunction(current, fallback),\n      provide: createProvideFunction({ current, fallback, messages }),\n    }\n  }\n}\n\nexport function createVuetifyAdapter (options?: LocaleOptions): LocaleInstance {\n  const current = shallowRef(options?.locale ?? 'en')\n  const fallback = shallowRef(options?.fallback ?? 'en')\n  const messages = ref({ en, ...options?.messages })\n\n  return {\n    name: 'vuetify',\n    current,\n    fallback,\n    messages,\n    t: createTranslateFunction(current, fallback, messages),\n    n: createNumberFunction(current, fallback),\n    provide: createProvideFunction({ current, fallback, messages }),\n  }\n}\n", "// Utilities\nimport { computed, inject, provide, ref, toRef } from 'vue'\nimport { createVuetifyAdapter } from '@/locale/adapters/vuetify'\n\n// Types\nimport type { InjectionKey, Ref } from 'vue'\n\nexport interface LocaleMessages {\n  [key: string]: LocaleMessages | string\n}\n\nexport interface LocaleOptions {\n  messages?: LocaleMessages\n  locale?: string\n  fallback?: string\n  adapter?: LocaleInstance\n}\n\nexport interface LocaleInstance {\n  name: string\n  messages: Ref<LocaleMessages>\n  current: Ref<string>\n  fallback: Ref<string>\n  t: (key: string, ...params: unknown[]) => string\n  n: (value: number) => string\n  provide: (props: LocaleOptions) => LocaleInstance\n}\n\nexport const LocaleSymbol: InjectionKey<LocaleInstance & RtlInstance> = Symbol.for('vuetify:locale')\n\nfunction isLocaleInstance (obj: any): obj is LocaleInstance {\n  return obj.name != null\n}\n\nexport function createLocale (options?: LocaleOptions & RtlOptions) {\n  const i18n = options?.adapter && isLocaleInstance(options?.adapter) ? options?.adapter : createVuetifyAdapter(options)\n  const rtl = createRtl(i18n, options)\n\n  return { ...i18n, ...rtl }\n}\n\nexport function useLocale () {\n  const locale = inject(LocaleSymbol)\n\n  if (!locale) throw new Error('[Vuetify] Could not find injected locale instance')\n\n  return locale\n}\n\nexport function provideLocale (props: LocaleOptions & RtlProps) {\n  const locale = inject(LocaleSymbol)\n\n  if (!locale) throw new Error('[Vuetify] Could not find injected locale instance')\n\n  const i18n = locale.provide(props)\n  const rtl = provideRtl(i18n, locale.rtl, props)\n\n  const data = { ...i18n, ...rtl }\n\n  provide(LocaleSymbol, data)\n\n  return data\n}\n\n// RTL\n\nexport interface RtlOptions {\n  rtl?: Record<string, boolean>\n}\n\nexport interface RtlProps {\n  rtl?: boolean\n}\n\nexport interface RtlInstance {\n  isRtl: Ref<boolean>\n  rtl: Ref<Record<string, boolean>>\n  rtlClasses: Ref<string>\n}\n\nexport const RtlSymbol: InjectionKey<RtlInstance> = Symbol.for('vuetify:rtl')\n\nfunction genDefaults () {\n  return {\n    af: false,\n    ar: true,\n    bg: false,\n    ca: false,\n    ckb: false,\n    cs: false,\n    de: false,\n    el: false,\n    en: false,\n    es: false,\n    et: false,\n    fa: true,\n    fi: false,\n    fr: false,\n    hr: false,\n    hu: false,\n    he: true,\n    id: false,\n    it: false,\n    ja: false,\n    km: false,\n    ko: false,\n    lv: false,\n    lt: false,\n    nl: false,\n    no: false,\n    pl: false,\n    pt: false,\n    ro: false,\n    ru: false,\n    sk: false,\n    sl: false,\n    srCyrl: false,\n    srLatn: false,\n    sv: false,\n    th: false,\n    tr: false,\n    az: false,\n    uk: false,\n    vi: false,\n    zhHans: false,\n    zhHant: false,\n  }\n}\n\nexport function createRtl (i18n: LocaleInstance, options?: RtlOptions): RtlInstance {\n  const rtl = ref<Record<string, boolean>>(options?.rtl ?? genDefaults())\n  const isRtl = computed(() => rtl.value[i18n.current.value] ?? false)\n\n  return {\n    isRtl,\n    rtl,\n    rtlClasses: toRef(() => `v-locale--is-${isRtl.value ? 'rtl' : 'ltr'}`),\n  }\n}\n\nexport function provideRtl (locale: LocaleInstance, rtl: RtlInstance['rtl'], props: RtlProps): RtlInstance {\n  const isRtl = computed(() => props.rtl ?? rtl.value[locale.current.value] ?? false)\n\n  return {\n    isRtl,\n    rtl,\n    rtlClasses: toRef(() => `v-locale--is-${isRtl.value ? 'rtl' : 'ltr'}`),\n  }\n}\n\nexport function useRtl () {\n  const locale = inject(LocaleSymbol)\n\n  if (!locale) throw new Error('[Vuetify] Could not find injected rtl instance')\n\n  return { isRtl: locale.isRtl, rtlClasses: locale.rtlClasses }\n}\n", "// Utilities\nimport { computed, inject, onScopeDispose, reactive, shallowRef, toRef, toRefs, watchEffect } from 'vue'\nimport { getCurrentInstanceName, mergeDeep, propsFactory } from '@/util'\nimport { IN_BROWSER, SUPPORTS_TOUCH } from '@/util/globals'\n\n// Types\nimport type { InjectionKey, PropType, Ref } from 'vue'\n\nexport const breakpoints = ['sm', 'md', 'lg', 'xl', 'xxl'] as const // no xs\n\nexport type Breakpoint = typeof breakpoints[number]\n\nexport type DisplayBreakpoint = 'xs' | Breakpoint\n\nexport type DisplayThresholds = {\n  [key in DisplayBreakpoint]: number\n}\n\nexport interface DisplayProps {\n  mobile?: boolean | null\n  mobileBreakpoint?: number | DisplayBreakpoint\n}\n\nexport interface DisplayOptions {\n  mobileBreakpoint?: number | DisplayBreakpoint\n  thresholds?: Partial<DisplayThresholds>\n}\n\nexport interface InternalDisplayOptions {\n  mobileBreakpoint: number | DisplayBreakpoint\n  thresholds: DisplayThresholds\n}\n\nexport type SSROptions = boolean | {\n  clientWidth: number\n  clientHeight?: number\n}\n\nexport interface DisplayPlatform {\n  android: boolean\n  ios: boolean\n  cordova: boolean\n  electron: boolean\n  chrome: boolean\n  edge: boolean\n  firefox: boolean\n  opera: boolean\n  win: boolean\n  mac: boolean\n  linux: boolean\n  touch: boolean\n  ssr: boolean\n}\n\nexport interface DisplayInstance {\n  xs: Ref<boolean>\n  sm: Ref<boolean>\n  md: Ref<boolean>\n  lg: Ref<boolean>\n  xl: Ref<boolean>\n  xxl: Ref<boolean>\n  smAndUp: Ref<boolean>\n  mdAndUp: Ref<boolean>\n  lgAndUp: Ref<boolean>\n  xlAndUp: Ref<boolean>\n  smAndDown: Ref<boolean>\n  mdAndDown: Ref<boolean>\n  lgAndDown: Ref<boolean>\n  xlAndDown: Ref<boolean>\n  name: Ref<DisplayBreakpoint>\n  height: Ref<number>\n  width: Ref<number>\n  mobile: Ref<boolean>\n  mobileBreakpoint: Ref<number | DisplayBreakpoint>\n  platform: Ref<DisplayPlatform>\n  thresholds: Ref<DisplayThresholds>\n\n  /** @internal */\n  ssr: boolean\n\n  update (): void\n}\n\nexport const DisplaySymbol: InjectionKey<DisplayInstance> = Symbol.for('vuetify:display')\n\nconst defaultDisplayOptions: DisplayOptions = {\n  mobileBreakpoint: 'lg',\n  thresholds: {\n    xs: 0,\n    sm: 600,\n    md: 960,\n    lg: 1280,\n    xl: 1920,\n    xxl: 2560,\n  },\n}\n\nconst parseDisplayOptions = (options: DisplayOptions = defaultDisplayOptions) => {\n  return mergeDeep(defaultDisplayOptions, options) as InternalDisplayOptions\n}\n\nfunction getClientWidth (ssr?: SSROptions) {\n  return IN_BROWSER && !ssr\n    ? window.innerWidth\n    : (typeof ssr === 'object' && ssr.clientWidth) || 0\n}\n\nfunction getClientHeight (ssr?: SSROptions) {\n  return IN_BROWSER && !ssr\n    ? window.innerHeight\n    : (typeof ssr === 'object' && ssr.clientHeight) || 0\n}\n\nfunction getPlatform (ssr?: SSROptions): DisplayPlatform {\n  const userAgent = IN_BROWSER && !ssr\n    ? window.navigator.userAgent\n    : 'ssr'\n\n  function match (regexp: RegExp) {\n    return Boolean(userAgent.match(regexp))\n  }\n\n  const android = match(/android/i)\n  const ios = match(/iphone|ipad|ipod/i)\n  const cordova = match(/cordova/i)\n  const electron = match(/electron/i)\n  const chrome = match(/chrome/i)\n  const edge = match(/edge/i)\n  const firefox = match(/firefox/i)\n  const opera = match(/opera/i)\n  const win = match(/win/i)\n  const mac = match(/mac/i)\n  const linux = match(/linux/i)\n\n  return {\n    android,\n    ios,\n    cordova,\n    electron,\n    chrome,\n    edge,\n    firefox,\n    opera,\n    win,\n    mac,\n    linux,\n    touch: SUPPORTS_TOUCH,\n    ssr: userAgent === 'ssr',\n  }\n}\n\nexport function createDisplay (options?: DisplayOptions, ssr?: SSROptions): DisplayInstance {\n  const { thresholds, mobileBreakpoint } = parseDisplayOptions(options)\n\n  const height = shallowRef(getClientHeight(ssr))\n  const platform = shallowRef(getPlatform(ssr))\n  const state = reactive({} as DisplayInstance)\n  const width = shallowRef(getClientWidth(ssr))\n\n  function updateSize () {\n    height.value = getClientHeight()\n    width.value = getClientWidth()\n  }\n  function update () {\n    updateSize()\n    platform.value = getPlatform()\n  }\n\n  // eslint-disable-next-line max-statements\n  watchEffect(() => {\n    const xs = width.value < thresholds.sm\n    const sm = width.value < thresholds.md && !xs\n    const md = width.value < thresholds.lg && !(sm || xs)\n    const lg = width.value < thresholds.xl && !(md || sm || xs)\n    const xl = width.value < thresholds.xxl && !(lg || md || sm || xs)\n    const xxl = width.value >= thresholds.xxl\n    const name =\n      xs ? 'xs'\n      : sm ? 'sm'\n      : md ? 'md'\n      : lg ? 'lg'\n      : xl ? 'xl'\n      : 'xxl'\n    const breakpointValue = typeof mobileBreakpoint === 'number' ? mobileBreakpoint : thresholds[mobileBreakpoint]\n    const mobile = width.value < breakpointValue\n\n    state.xs = xs\n    state.sm = sm\n    state.md = md\n    state.lg = lg\n    state.xl = xl\n    state.xxl = xxl\n    state.smAndUp = !xs\n    state.mdAndUp = !(xs || sm)\n    state.lgAndUp = !(xs || sm || md)\n    state.xlAndUp = !(xs || sm || md || lg)\n    state.smAndDown = !(md || lg || xl || xxl)\n    state.mdAndDown = !(lg || xl || xxl)\n    state.lgAndDown = !(xl || xxl)\n    state.xlAndDown = !xxl\n    state.name = name\n    state.height = height.value\n    state.width = width.value\n    state.mobile = mobile\n    state.mobileBreakpoint = mobileBreakpoint\n    state.platform = platform.value\n    state.thresholds = thresholds\n  })\n\n  if (IN_BROWSER) {\n    window.addEventListener('resize', updateSize, { passive: true })\n\n    onScopeDispose(() => {\n      window.removeEventListener('resize', updateSize)\n    }, true)\n  }\n\n  return { ...toRefs(state), update, ssr: !!ssr }\n}\n\nexport const makeDisplayProps = propsFactory({\n  mobile: {\n    type: Boolean as PropType<boolean | null>,\n    default: false,\n  },\n  mobileBreakpoint: [Number, String] as PropType<number | DisplayBreakpoint>,\n}, 'display')\n\nexport function useDisplay (\n  props: DisplayProps = { mobile: null },\n  name = getCurrentInstanceName(),\n) {\n  const display = inject(DisplaySymbol)\n\n  if (!display) throw new Error('Could not find Vuetify display injection')\n\n  const mobile = computed(() => {\n    if (props.mobile) {\n      return true\n    } else if (typeof props.mobileBreakpoint === 'number') {\n      return display.width.value < props.mobileBreakpoint\n    } else if (props.mobileBreakpoint) {\n      return display.width.value < display.thresholds.value[props.mobileBreakpoint]\n    } else if (props.mobile === null) {\n      return display.mobile.value\n    } else {\n      return false\n    }\n  })\n\n  const displayClasses = toRef(() => {\n    if (!name) return {}\n\n    return { [`${name}--mobile`]: mobile.value }\n  })\n\n  return { ...display, displayClasses, mobile }\n}\n", "// Utilities\nimport {\n  computed,\n  inject,\n  provide,\n  ref,\n  shallowRef,\n  toRef,\n  watch,\n  watchEffect,\n} from 'vue'\nimport {\n  createRange,\n  darken,\n  getCurrentInstance,\n  getForeground,\n  getLuma,\n  IN_BROWSER,\n  lighten,\n  mergeDeep,\n  parseColor,\n  propsFactory,\n  RGBtoHex,\n} from '@/util'\n\n// Types\nimport type { VueHeadClient } from '@unhead/vue/client'\nimport type { HeadClient } from '@vueuse/head'\nimport type { App, DeepReadonly, InjectionKey, Ref } from 'vue'\n\ntype DeepPartial<T> = T extends object ? { [P in keyof T]?: DeepPartial<T[P]> } : T\n\nexport type ThemeOptions = false | {\n  cspNonce?: string\n  defaultTheme?: string\n  variations?: false | VariationsOptions\n  themes?: Record<string, ThemeDefinition>\n  stylesheetId?: string\n  scope?: string\n}\nexport type ThemeDefinition = DeepPartial<InternalThemeDefinition>\n\ninterface InternalThemeOptions {\n  cspNonce?: string\n  isDisabled: boolean\n  defaultTheme: string\n  variations: false | VariationsOptions\n  themes: Record<string, InternalThemeDefinition>\n  stylesheetId: string\n  scope?: string\n}\n\ninterface VariationsOptions {\n  colors: string[]\n  lighten: number\n  darken: number\n}\n\ninterface InternalThemeDefinition {\n  dark: boolean\n  colors: Colors\n  variables: Record<string, string | number>\n}\n\nexport interface Colors extends BaseColors, OnColors {\n  [key: string]: string\n}\n\ninterface BaseColors {\n  background: string\n  surface: string\n  primary: string\n  secondary: string\n  success: string\n  warning: string\n  error: string\n  info: string\n}\n\ninterface OnColors {\n  'on-background': string\n  'on-surface': string\n  'on-primary': string\n  'on-secondary': string\n  'on-success': string\n  'on-warning': string\n  'on-error': string\n  'on-info': string\n}\n\nexport interface ThemeInstance {\n  readonly isDisabled: boolean\n  readonly themes: Ref<Record<string, InternalThemeDefinition>>\n\n  readonly name: Readonly<Ref<string>>\n  readonly current: DeepReadonly<Ref<InternalThemeDefinition>>\n  readonly computedThemes: DeepReadonly<Ref<Record<string, InternalThemeDefinition>>>\n\n  readonly themeClasses: Readonly<Ref<string | undefined>>\n  readonly styles: Readonly<Ref<string>>\n\n  readonly global: {\n    readonly name: Ref<string>\n    readonly current: DeepReadonly<Ref<InternalThemeDefinition>>\n  }\n}\n\nexport const ThemeSymbol: InjectionKey<ThemeInstance> = Symbol.for('vuetify:theme')\n\nexport const makeThemeProps = propsFactory({\n  theme: String,\n}, 'theme')\n\nfunction genDefaults () {\n  return {\n    defaultTheme: 'light',\n    variations: { colors: [], lighten: 0, darken: 0 },\n    themes: {\n      light: {\n        dark: false,\n        colors: {\n          background: '#FFFFFF',\n          surface: '#FFFFFF',\n          'surface-bright': '#FFFFFF',\n          'surface-light': '#EEEEEE',\n          'surface-variant': '#424242',\n          'on-surface-variant': '#EEEEEE',\n          primary: '#1867C0',\n          'primary-darken-1': '#1F5592',\n          secondary: '#48A9A6',\n          'secondary-darken-1': '#018786',\n          error: '#B00020',\n          info: '#2196F3',\n          success: '#4CAF50',\n          warning: '#FB8C00',\n        },\n        variables: {\n          'border-color': '#000000',\n          'border-opacity': 0.12,\n          'high-emphasis-opacity': 0.87,\n          'medium-emphasis-opacity': 0.60,\n          'disabled-opacity': 0.38,\n          'idle-opacity': 0.04,\n          'hover-opacity': 0.04,\n          'focus-opacity': 0.12,\n          'selected-opacity': 0.08,\n          'activated-opacity': 0.12,\n          'pressed-opacity': 0.12,\n          'dragged-opacity': 0.08,\n          'theme-kbd': '#212529',\n          'theme-on-kbd': '#FFFFFF',\n          'theme-code': '#F5F5F5',\n          'theme-on-code': '#000000',\n        },\n      },\n      dark: {\n        dark: true,\n        colors: {\n          background: '#121212',\n          surface: '#212121',\n          'surface-bright': '#ccbfd6',\n          'surface-light': '#424242',\n          'surface-variant': '#c8c8c8',\n          'on-surface-variant': '#000000',\n          primary: '#2196F3',\n          'primary-darken-1': '#277CC1',\n          secondary: '#54B6B2',\n          'secondary-darken-1': '#48A9A6',\n          error: '#CF6679',\n          info: '#2196F3',\n          success: '#4CAF50',\n          warning: '#FB8C00',\n        },\n        variables: {\n          'border-color': '#FFFFFF',\n          'border-opacity': 0.12,\n          'high-emphasis-opacity': 1,\n          'medium-emphasis-opacity': 0.70,\n          'disabled-opacity': 0.50,\n          'idle-opacity': 0.10,\n          'hover-opacity': 0.04,\n          'focus-opacity': 0.12,\n          'selected-opacity': 0.08,\n          'activated-opacity': 0.12,\n          'pressed-opacity': 0.16,\n          'dragged-opacity': 0.08,\n          'theme-kbd': '#212529',\n          'theme-on-kbd': '#FFFFFF',\n          'theme-code': '#343434',\n          'theme-on-code': '#CCCCCC',\n        },\n      },\n    },\n    stylesheetId: 'vuetify-theme-stylesheet',\n  }\n}\n\nfunction parseThemeOptions (options: ThemeOptions = genDefaults()): InternalThemeOptions {\n  const defaults = genDefaults()\n\n  if (!options) return { ...defaults, isDisabled: true } as any\n\n  const themes: Record<string, InternalThemeDefinition> = {}\n  for (const [key, theme] of Object.entries(options.themes ?? {})) {\n    const defaultTheme = theme.dark || key === 'dark'\n      ? defaults.themes?.dark\n      : defaults.themes?.light\n    themes[key] = mergeDeep(defaultTheme, theme) as InternalThemeDefinition\n  }\n\n  return mergeDeep(\n    defaults,\n    { ...options, themes },\n  ) as InternalThemeOptions\n}\n\nfunction createCssClass (lines: string[], selector: string, content: string[], scope?: string) {\n  lines.push(\n    `${getScopedSelector(selector, scope)} {\\n`,\n    ...content.map(line => `  ${line};\\n`),\n    '}\\n',\n  )\n}\n\nfunction genCssVariables (theme: InternalThemeDefinition) {\n  const lightOverlay = theme.dark ? 2 : 1\n  const darkOverlay = theme.dark ? 1 : 2\n\n  const variables: string[] = []\n  for (const [key, value] of Object.entries(theme.colors)) {\n    const rgb = parseColor(value)\n    variables.push(`--v-theme-${key}: ${rgb.r},${rgb.g},${rgb.b}`)\n    if (!key.startsWith('on-')) {\n      variables.push(`--v-theme-${key}-overlay-multiplier: ${getLuma(value) > 0.18 ? lightOverlay : darkOverlay}`)\n    }\n  }\n\n  for (const [key, value] of Object.entries(theme.variables)) {\n    const color = typeof value === 'string' && value.startsWith('#') ? parseColor(value) : undefined\n    const rgb = color ? `${color.r}, ${color.g}, ${color.b}` : undefined\n    variables.push(`--v-${key}: ${rgb ?? value}`)\n  }\n\n  return variables\n}\n\nfunction genVariation (name: string, color: string, variations: VariationsOptions | false) {\n  const object: Record<string, string> = {}\n  if (variations) {\n    for (const variation of (['lighten', 'darken'] as const)) {\n      const fn = variation === 'lighten' ? lighten : darken\n      for (const amount of createRange(variations[variation], 1)) {\n        object[`${name}-${variation}-${amount}`] = RGBtoHex(fn(parseColor(color), amount))\n      }\n    }\n  }\n  return object\n}\n\nfunction genVariations (colors: InternalThemeDefinition['colors'], variations: VariationsOptions | false) {\n  if (!variations) return {}\n\n  let variationColors = {}\n  for (const name of variations.colors) {\n    const color = colors[name]\n\n    if (!color) continue\n\n    variationColors = {\n      ...variationColors,\n      ...genVariation(name, color, variations),\n    }\n  }\n  return variationColors\n}\n\nfunction genOnColors (colors: InternalThemeDefinition['colors']) {\n  const onColors = {} as InternalThemeDefinition['colors']\n\n  for (const color of Object.keys(colors)) {\n    if (color.startsWith('on-') || colors[`on-${color}`]) continue\n\n    const onColor = `on-${color}` as keyof OnColors\n    const colorVal = parseColor(colors[color])\n\n    onColors[onColor] = getForeground(colorVal)\n  }\n\n  return onColors\n}\n\nfunction getScopedSelector (selector: string, scope?: string) {\n  if (!scope) return selector\n\n  const scopeSelector = `:where(${scope})`\n\n  return selector === ':root' ? scopeSelector : `${scopeSelector} ${selector}`\n}\n\nfunction upsertStyles (styleEl: HTMLStyleElement | null, styles: string) {\n  if (!styleEl) return\n\n  styleEl.innerHTML = styles\n}\n\nfunction getOrCreateStyleElement (id: string, cspNonce?: string) {\n  if (!IN_BROWSER) return null\n\n  let style = document.getElementById(id) as HTMLStyleElement | null\n\n  if (!style) {\n    style = document.createElement('style')\n    style.id = id\n    style.type = 'text/css'\n\n    if (cspNonce) style.setAttribute('nonce', cspNonce)\n\n    document.head.appendChild(style)\n  }\n\n  return style\n}\n\n// Composables\nexport function createTheme (options?: ThemeOptions): ThemeInstance & { install: (app: App) => void } {\n  const parsedOptions = parseThemeOptions(options)\n  const name = shallowRef(parsedOptions.defaultTheme)\n  const themes = ref(parsedOptions.themes)\n\n  const computedThemes = computed(() => {\n    const acc: Record<string, InternalThemeDefinition> = {}\n    for (const [name, original] of Object.entries(themes.value)) {\n      const colors = {\n        ...original.colors,\n        ...genVariations(original.colors, parsedOptions.variations),\n      }\n\n      acc[name] = {\n        ...original,\n        colors: {\n          ...colors,\n          ...genOnColors(colors),\n        },\n      }\n    }\n    return acc\n  })\n\n  const current = toRef(() => computedThemes.value[name.value])\n\n  const styles = computed(() => {\n    const lines: string[] = []\n\n    if (current.value?.dark) {\n      createCssClass(lines, ':root', ['color-scheme: dark'], parsedOptions.scope)\n    }\n\n    createCssClass(lines, ':root', genCssVariables(current.value), parsedOptions.scope)\n\n    for (const [themeName, theme] of Object.entries(computedThemes.value)) {\n      createCssClass(lines, `.v-theme--${themeName}`, [\n        `color-scheme: ${theme.dark ? 'dark' : 'normal'}`,\n        ...genCssVariables(theme),\n      ], parsedOptions.scope)\n    }\n\n    const bgLines: string[] = []\n    const fgLines: string[] = []\n\n    const colors = new Set(Object.values(computedThemes.value).flatMap(theme => Object.keys(theme.colors)))\n    for (const key of colors) {\n      if (key.startsWith('on-')) {\n        createCssClass(fgLines, `.${key}`, [`color: rgb(var(--v-theme-${key})) !important`], parsedOptions.scope)\n      } else {\n        createCssClass(bgLines, `.bg-${key}`, [\n          `--v-theme-overlay-multiplier: var(--v-theme-${key}-overlay-multiplier)`,\n          `background-color: rgb(var(--v-theme-${key})) !important`,\n          `color: rgb(var(--v-theme-on-${key})) !important`,\n        ], parsedOptions.scope)\n        createCssClass(fgLines, `.text-${key}`, [`color: rgb(var(--v-theme-${key})) !important`], parsedOptions.scope)\n        createCssClass(fgLines, `.border-${key}`, [`--v-border-color: var(--v-theme-${key})`], parsedOptions.scope)\n      }\n    }\n\n    lines.push(...bgLines, ...fgLines)\n\n    return lines.map((str, i) => i === 0 ? str : `    ${str}`).join('')\n  })\n\n  function install (app: App) {\n    if (parsedOptions.isDisabled) return\n\n    const head = app._context.provides.usehead as HeadClient & VueHeadClient<any> | undefined\n    if (head) {\n      function getHead () {\n        return {\n          style: [{\n            textContent: styles.value,\n            id: parsedOptions.stylesheetId,\n            nonce: parsedOptions.cspNonce || false as never,\n          }],\n        }\n      }\n\n      if (head.push) {\n        const entry = head.push(getHead)\n        if (IN_BROWSER) {\n          watch(styles, () => { entry.patch(getHead) })\n        }\n      } else {\n        if (IN_BROWSER) {\n          head.addHeadObjs(toRef(getHead))\n          watchEffect(() => head.updateDOM())\n        } else {\n          head.addHeadObjs(getHead())\n        }\n      }\n    } else {\n      if (IN_BROWSER) {\n        watch(styles, updateStyles, { immediate: true })\n      } else {\n        updateStyles()\n      }\n\n      function updateStyles () {\n        upsertStyles(\n          getOrCreateStyleElement(parsedOptions.stylesheetId, parsedOptions.cspNonce),\n          styles.value\n        )\n      }\n    }\n  }\n\n  const themeClasses = toRef(() => parsedOptions.isDisabled ? undefined : `v-theme--${name.value}`)\n\n  return {\n    install,\n    isDisabled: parsedOptions.isDisabled,\n    name,\n    themes,\n    current,\n    computedThemes,\n    themeClasses,\n    styles,\n    global: {\n      name,\n      current,\n    },\n  }\n}\n\nexport function provideTheme (props: { theme?: string }) {\n  getCurrentInstance('provideTheme')\n\n  const theme = inject(ThemeSymbol, null)\n\n  if (!theme) throw new Error('Could not find Vuetify theme injection')\n\n  const name = toRef(() => props.theme ?? theme.name.value)\n  const current = toRef(() => theme.themes.value[name.value])\n\n  const themeClasses = toRef(() => theme.isDisabled ? undefined : `v-theme--${name.value}`)\n\n  const newTheme: ThemeInstance = {\n    ...theme,\n    name,\n    current,\n    themeClasses,\n  }\n\n  provide(ThemeSymbol, newTheme)\n\n  return newTheme\n}\n\nexport function useTheme () {\n  getCurrentInstance('useTheme')\n\n  const theme = inject(ThemeSymbol, null)\n\n  if (!theme) throw new Error('Could not find Vuetify theme injection')\n\n  return theme\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMO,SAASA,eAAgBC,QAA8BC,IAAiC;AAC7F,MAAIC;AACJ,WAASC,QAAS;AAChBD,YAAQE,YAAY;AACpBF,UAAMG,IAAI,MAAMJ,GAAGK,SACfL,GAAG,MAAM;AAAEC,qCAAOK;AAAQJ,YAAM;IAAE,CAAC,IAClCF,GAAW,CAChB;EACF;AAEAO,QAAMR,QAAQS,YAAU;AACtB,QAAIA,UAAU,CAACP,OAAO;AACpBC,YAAM;IACR,WAAW,CAACM,QAAQ;AAClBP,qCAAOK;AACPL,cAAQQ;IACV;EACF,GAAG;IAAEC,WAAW;EAAK,CAAC;AAEtBC,iBAAe,MAAM;AACnBV,mCAAOK;EACT,CAAC;AACH;;;ACdO,SAASM,gBAKdC,OACAC,MACAC,cAGA;AAAA,MAFAC,cAA2CC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAIG,OAAWA;AAAC,MAC3DC,eAA2CJ,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAIG,OAAWA;AAE1D,QAAME,KAAKC,mBAAmB,iBAAiB;AAC/C,QAAMC,WAAWC,IAAIZ,MAAMC,IAAI,MAAMK,SAAYN,MAAMC,IAAI,IAAIC,YAAY;AAC3E,QAAMW,YAAYC,YAAYb,IAAI;AAClC,QAAMc,aAAaF,cAAcZ;AAEjC,QAAMe,eAAeD,aACjBE,SAAS,MAAM;AA/BrB;AAgCM,SAAKjB,MAAMC,IAAI;AACf,WAAO,CAAC,KACLQ,QAAGS,MAAMlB,UAATS,mBAAgBU,eAAelB,YAASQ,QAAGS,MAAMlB,UAATS,mBAAgBU,eAAeN,mBACvEJ,QAAGS,MAAMlB,UAATS,mBAAgBU,eAAe,YAAYlB,IAAI,UAAOQ,QAAGS,MAAMlB,UAATS,mBAAgBU,eAAe,YAAYN,SAAS;EAE/G,CAAC,IACCI,SAAS,MAAM;AAtCrB;AAuCM,SAAKjB,MAAMC,IAAI;AACf,WAAO,CAAC,IAAEQ,QAAGS,MAAMlB,UAATS,mBAAgBU,eAAelB,YAASQ,QAAGS,MAAMlB,UAATS,mBAAgBU,eAAe,YAAYlB,IAAI;EACnG,CAAC;AAEHmB,iBAAe,MAAM,CAACJ,aAAaK,OAAO,MAAM;AAC9CC,UAAM,MAAMtB,MAAMC,IAAI,GAAGsB,SAAO;AAC9BZ,eAASU,QAAQE;IACnB,CAAC;EACH,CAAC;AAED,QAAMC,QAAQP,SAAS;IACrBQ,MAAY;AACV,YAAMC,gBAAgB1B,MAAMC,IAAI;AAChC,aAAOE,YAAYa,aAAaK,QAAQK,gBAAgBf,SAASU,KAAK;IACxE;IACAM,IAAKC,eAAe;AAClB,YAAMC,WAAWrB,aAAaoB,aAAa;AAC3C,YAAMP,QAAQS,MAAMd,aAAaK,QAAQrB,MAAMC,IAAI,IAAIU,SAASU,KAAK;AACrE,UAAIA,UAAUQ,YAAY1B,YAAYkB,KAAK,MAAMO,eAAe;AAC9D;MACF;AACAjB,eAASU,QAAQQ;AACjBpB,+BAAIsB,KAAK,UAAU9B,IAAI,IAAI4B;IAC7B;EACF,CAAC;AAEDG,SAAOC,eAAeT,OAAO,iBAAiB;IAC5CC,KAAKA,MAAMT,aAAaK,QAAQrB,MAAMC,IAAI,IAAIU,SAASU;EACzD,CAAC;AAED,SAAOG;AACT;;;ACtEA,IAAA,aAAe;EACbU,OAAO;EACPC,MAAM;EACNC,OAAO;EACPC,SAAS;EACTC,aAAa;IACXC,IAAI;IACJC,QAAQ;EACV;EACAC,cAAc;IACZC,eAAe;IACfC,aAAa;EACf;EACAC,WAAW;IACTC,kBAAkB;IAClBC,WAAW;MACTC,gBAAgB;MAChBC,eAAe;MACfC,UAAU;MACVC,cAAc;MACdC,oBAAoB;MACpBC,mBAAmB;IACrB;IACAC,QAAQ;EACV;EACAC,YAAY;IACVT,kBAAkB;IAClBU,iBAAiB;IACjBC,UAAU;IACVC,UAAU;IACVC,WAAW;IACXC,UAAU;IACVC,UAAU;EACZ;EACAC,gBAAgB;IACdC,SAAS;EACX;EACAC,YAAY;IACVC,eAAe;IACfC,OAAO;MACLC,OAAO;MACPC,QAAQ;IACV;IACAD,OAAO;IACPC,QAAQ;IACRC,OAAO;MACLC,aAAa;IACf;EACF;EACAC,YAAY;EACZC,UAAU;IACRC,MAAM;IACNC,MAAM;IACN3B,WAAW;MACT4B,WAAW;IACb;EACF;EACAC,UAAU;IACRC,YAAY;IACZC,OAAO;EACT;EACAT,OAAO;IACLU,OAAO;IACPC,eAAe;IACfC,cAAc;IACdC,KAAK;EACP;EACAC,WAAW;IACTC,SAAS;IACTC,aAAa;EACf;EACAC,YAAY;IACVnB,OAAO;IACPJ,SAAS;IACTwB,QAAQ;EACV;EACAC,YAAY;IACVC,IAAI;IACJC,IAAI;IACJvB,OAAO;EACT;EACAwB,YAAY;IACV5C,WAAW;MACT6C,MAAM;MACNlB,MAAM;MACNmB,UAAU;MACVC,MAAM;MACNC,aAAa;MACbC,OAAO;MACPC,MAAM;IACR;EACF;EACAC,SAAS;IACPxB,MAAM;IACND,MAAM;EACR;EACA0B,QAAQ;IACNpD,WAAW;MACTqD,MAAM;IACR;EACF;EACAC,SAAS;EACTC,gBAAgB;IACdC,UAAU;IACVC,OAAO;EACT;EACAC,OAAO;IACLC,UAAU;IACVC,OAAO;IACPC,QAAQ;IACRC,SAAS;IACTC,SAAS;IACTC,WAAW;IACXC,WAAW;IACXC,cAAc;IACdC,SAAS;IACTC,UAAU;IACVC,SAAS;EACX;AACF;;;ACzGA,IAAMC,cAAc;AAEpB,IAAMC,UAAUA,CAACC,KAAaC,WAAsB;AAClD,SAAOD,IAAID,QAAQ,cAAc,CAACG,OAAeC,UAAkB;AACjE,WAAOC,OAAOH,OAAOI,OAAOF,KAAK,CAAC,CAAC;EACrC,CAAC;AACH;AAEA,IAAMG,0BAA0BA,CAC9BC,SACAC,UACAC,aACG;AACH,SAAO,SAACC,KAAsC;AAAA,aAAAC,OAAAC,UAAAC,QAAtBZ,SAAM,IAAAa,MAAAH,OAAA,IAAAA,OAAA,IAAA,CAAA,GAAAI,OAAA,GAAAA,OAAAJ,MAAAI,QAAA;AAANd,aAAMc,OAAA,CAAA,IAAAH,UAAAG,IAAA;IAAA;AAC5B,QAAI,CAACL,IAAIM,WAAWlB,WAAW,GAAG;AAChC,aAAOC,QAAQW,KAAKT,MAAM;IAC5B;AAEA,UAAMgB,WAAWP,IAAIX,QAAQD,aAAa,EAAE;AAC5C,UAAMoB,gBAAgBX,QAAQY,SAASV,SAASU,MAAMZ,QAAQY,KAAK;AACnE,UAAMC,iBAAiBZ,SAASW,SAASV,SAASU,MAAMX,SAASW,KAAK;AAEtE,QAAInB,MAAcqB,qBAAqBH,eAAeD,UAAU,IAAI;AAEpE,QAAI,CAACjB,KAAK;AACRsB,kBAAY,oBAAoBZ,GAAG,mBAAmBH,QAAQY,KAAK,2BAA2B;AAC9FnB,YAAMqB,qBAAqBD,gBAAgBH,UAAU,IAAI;IAC3D;AAEA,QAAI,CAACjB,KAAK;AACRuB,mBAAa,oBAAoBb,GAAG,yBAAyB;AAC7DV,YAAMU;IACR;AAEA,QAAI,OAAOV,QAAQ,UAAU;AAC3BuB,mBAAa,oBAAoBb,GAAG,0BAA0B;AAC9DV,YAAMU;IACR;AAEA,WAAOX,QAAQC,KAAKC,MAAM;EAC5B;AACF;AAEA,SAASuB,qBAAsBjB,SAAsBC,UAAuB;AAC1E,SAAO,CAACW,OAAeM,YAAuC;AAC5D,UAAMC,eAAe,IAAIC,KAAKC,aAAa,CAACrB,QAAQY,OAAOX,SAASW,KAAK,GAAGM,OAAO;AAEnF,WAAOC,aAAaG,OAAOV,KAAK;EAClC;AACF;AAEA,SAASW,YAAiBC,OAAYC,MAAcC,UAAkB;AACpE,QAAMC,WAAWC,gBAAgBJ,OAAOC,MAAMD,MAAMC,IAAI,KAAKC,SAASd,KAAK;AAG3Ee,WAASf,QAAQY,MAAMC,IAAI,KAAKC,SAASd;AAEzCiB,QAAMH,UAAUI,OAAK;AACnB,QAAIN,MAAMC,IAAI,KAAK,MAAM;AACvBE,eAASf,QAAQc,SAASd;IAC5B;EACF,CAAC;AAED,SAAOe;AACT;AAEA,SAASI,sBAAuBC,OAAuF;AACrH,SAAQR,WAAyC;AAC/C,UAAMxB,UAAUuB,YAAYC,OAAO,UAAUQ,MAAMhC,OAAO;AAC1D,UAAMC,WAAWsB,YAAYC,OAAO,YAAYQ,MAAM/B,QAAQ;AAC9D,UAAMC,WAAWqB,YAAYC,OAAO,YAAYQ,MAAM9B,QAAQ;AAE9D,WAAO;MACL+B,MAAM;MACNjC;MACAC;MACAC;MACAgC,GAAGnC,wBAAwBC,SAASC,UAAUC,QAAQ;MACtDiC,GAAGlB,qBAAqBjB,SAASC,QAAQ;MACzCmC,SAASL,sBAAsB;QAAE/B;QAASC;QAAUC;MAAS,CAAC;IAChE;EACF;AACF;AAEO,SAASmC,qBAAsBnB,SAAyC;AAC7E,QAAMlB,UAAUsC,YAAWpB,mCAASqB,WAAU,IAAI;AAClD,QAAMtC,WAAWqC,YAAWpB,mCAASjB,aAAY,IAAI;AACrD,QAAMC,WAAWsC,IAAI;IAAEC;IAAI,GAAGvB,mCAAShB;EAAS,CAAC;AAEjD,SAAO;IACL+B,MAAM;IACNjC;IACAC;IACAC;IACAgC,GAAGnC,wBAAwBC,SAASC,UAAUC,QAAQ;IACtDiC,GAAGlB,qBAAqBjB,SAASC,QAAQ;IACzCmC,SAASL,sBAAsB;MAAE/B;MAASC;MAAUC;IAAS,CAAC;EAChE;AACF;;;ACpFO,IAAMwC,eAA2DC,OAAOC,IAAI,gBAAgB;AAEnG,SAASC,iBAAkBC,KAAiC;AAC1D,SAAOA,IAAIC,QAAQ;AACrB;AAEO,SAASC,aAAcC,SAAsC;AAClE,QAAMC,QAAOD,mCAASE,YAAWN,iBAAiBI,mCAASE,OAAO,IAAIF,mCAASE,UAAUC,qBAAqBH,OAAO;AACrH,QAAMI,MAAMC,UAAUJ,MAAMD,OAAO;AAEnC,SAAO;IAAE,GAAGC;IAAM,GAAGG;EAAI;AAC3B;AAEO,SAASE,YAAa;AAC3B,QAAMC,SAASC,OAAOf,YAAY;AAElC,MAAI,CAACc,OAAQ,OAAM,IAAIE,MAAM,mDAAmD;AAEhF,SAAOF;AACT;AAEO,SAASG,cAAeC,OAAiC;AAC9D,QAAMJ,SAASC,OAAOf,YAAY;AAElC,MAAI,CAACc,OAAQ,OAAM,IAAIE,MAAM,mDAAmD;AAEhF,QAAMR,OAAOM,OAAOK,QAAQD,KAAK;AACjC,QAAMP,MAAMS,WAAWZ,MAAMM,OAAOH,KAAKO,KAAK;AAE9C,QAAMG,OAAO;IAAE,GAAGb;IAAM,GAAGG;EAAI;AAE/BQ,UAAQnB,cAAcqB,IAAI;AAE1B,SAAOA;AACT;AAkBO,IAAMC,YAAuCrB,OAAOC,IAAI,aAAa;AAE5E,SAASqB,cAAe;AACtB,SAAO;IACLC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,KAAK;IACLC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRC,QAAQ;IACRC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,QAAQ;IACRC,QAAQ;EACV;AACF;AAEO,SAASrD,UAAWJ,MAAsBD,SAAmC;AAClF,QAAMI,MAAMuD,KAA6B3D,mCAASI,QAAOY,YAAY,CAAC;AACtE,QAAM4C,QAAQC,SAAS,MAAMzD,IAAI0D,MAAM7D,KAAK8D,QAAQD,KAAK,KAAK,KAAK;AAEnE,SAAO;IACLF;IACAxD;IACA4D,YAAYC,MAAM,MAAM,gBAAgBL,MAAME,QAAQ,QAAQ,KAAK,EAAE;EACvE;AACF;AAEO,SAASjD,WAAYN,QAAwBH,KAAyBO,OAA8B;AACzG,QAAMiD,QAAQC,SAAS,MAAMlD,MAAMP,OAAOA,IAAI0D,MAAMvD,OAAOwD,QAAQD,KAAK,KAAK,KAAK;AAElF,SAAO;IACLF;IACAxD;IACA4D,YAAYC,MAAM,MAAM,gBAAgBL,MAAME,QAAQ,QAAQ,KAAK,EAAE;EACvE;AACF;AAEO,SAASI,SAAU;AACxB,QAAM3D,SAASC,OAAOf,YAAY;AAElC,MAAI,CAACc,OAAQ,OAAM,IAAIE,MAAM,gDAAgD;AAE7E,SAAO;IAAEmD,OAAOrD,OAAOqD;IAAOI,YAAYzD,OAAOyD;EAAW;AAC9D;;;ACpJO,IAAMG,cAAc,CAAC,MAAM,MAAM,MAAM,MAAM,KAAK;AA2ElD,IAAMC,gBAA+CC,OAAOC,IAAI,iBAAiB;AAExF,IAAMC,wBAAwC;EAC5CC,kBAAkB;EAClBC,YAAY;IACVC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,IAAI;IACJC,KAAK;EACP;AACF;AAEA,IAAMC,sBAAsB,WAAqD;AAAA,MAApDC,UAAuBC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAGX;AACrD,SAAOc,UAAUd,uBAAuBU,OAAO;AACjD;AAEA,SAASK,eAAgBC,KAAkB;AACzC,SAAOC,cAAc,CAACD,MAClBE,OAAOC,aACN,OAAOH,QAAQ,YAAYA,IAAII,eAAgB;AACtD;AAEA,SAASC,gBAAiBL,KAAkB;AAC1C,SAAOC,cAAc,CAACD,MAClBE,OAAOI,cACN,OAAON,QAAQ,YAAYA,IAAIO,gBAAiB;AACvD;AAEA,SAASC,YAAaR,KAAmC;AACvD,QAAMS,YAAYR,cAAc,CAACD,MAC7BE,OAAOQ,UAAUD,YACjB;AAEJ,WAASE,MAAOC,QAAgB;AAC9B,WAAOC,QAAQJ,UAAUE,MAAMC,MAAM,CAAC;EACxC;AAEA,QAAME,UAAUH,MAAM,UAAU;AAChC,QAAMI,MAAMJ,MAAM,mBAAmB;AACrC,QAAMK,UAAUL,MAAM,UAAU;AAChC,QAAMM,WAAWN,MAAM,WAAW;AAClC,QAAMO,SAASP,MAAM,SAAS;AAC9B,QAAMQ,OAAOR,MAAM,OAAO;AAC1B,QAAMS,UAAUT,MAAM,UAAU;AAChC,QAAMU,QAAQV,MAAM,QAAQ;AAC5B,QAAMW,MAAMX,MAAM,MAAM;AACxB,QAAMY,MAAMZ,MAAM,MAAM;AACxB,QAAMa,QAAQb,MAAM,QAAQ;AAE5B,SAAO;IACLG;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC;IACAC,OAAOC;IACP1B,KAAKS,cAAc;EACrB;AACF;AAEO,SAASkB,cAAejC,SAA0BM,KAAmC;AAC1F,QAAM;IAAEd;IAAYD;EAAiB,IAAIQ,oBAAoBC,OAAO;AAEpE,QAAMkC,SAASC,WAAWxB,gBAAgBL,GAAG,CAAC;AAC9C,QAAM8B,WAAWD,WAAWrB,YAAYR,GAAG,CAAC;AAC5C,QAAM+B,QAAQC,SAAS,CAAC,CAAoB;AAC5C,QAAMC,QAAQJ,WAAW9B,eAAeC,GAAG,CAAC;AAE5C,WAASkC,aAAc;AACrBN,WAAOO,QAAQ9B,gBAAgB;AAC/B4B,UAAME,QAAQpC,eAAe;EAC/B;AACA,WAASqC,SAAU;AACjBF,eAAW;AACXJ,aAASK,QAAQ3B,YAAY;EAC/B;AAGA6B,cAAY,MAAM;AAChB,UAAMlD,KAAK8C,MAAME,QAAQjD,WAAWE;AACpC,UAAMA,KAAK6C,MAAME,QAAQjD,WAAWG,MAAM,CAACF;AAC3C,UAAME,KAAK4C,MAAME,QAAQjD,WAAWI,MAAM,EAAEF,MAAMD;AAClD,UAAMG,KAAK2C,MAAME,QAAQjD,WAAWK,MAAM,EAAEF,MAAMD,MAAMD;AACxD,UAAMI,KAAK0C,MAAME,QAAQjD,WAAWM,OAAO,EAAEF,MAAMD,MAAMD,MAAMD;AAC/D,UAAMK,MAAMyC,MAAME,SAASjD,WAAWM;AACtC,UAAM8C,OACJnD,KAAK,OACHC,KAAK,OACLC,KAAK,OACLC,KAAK,OACLC,KAAK,OACL;AACJ,UAAMgD,kBAAkB,OAAOtD,qBAAqB,WAAWA,mBAAmBC,WAAWD,gBAAgB;AAC7G,UAAMuD,SAASP,MAAME,QAAQI;AAE7BR,UAAM5C,KAAKA;AACX4C,UAAM3C,KAAKA;AACX2C,UAAM1C,KAAKA;AACX0C,UAAMzC,KAAKA;AACXyC,UAAMxC,KAAKA;AACXwC,UAAMvC,MAAMA;AACZuC,UAAMU,UAAU,CAACtD;AACjB4C,UAAMW,UAAU,EAAEvD,MAAMC;AACxB2C,UAAMY,UAAU,EAAExD,MAAMC,MAAMC;AAC9B0C,UAAMa,UAAU,EAAEzD,MAAMC,MAAMC,MAAMC;AACpCyC,UAAMc,YAAY,EAAExD,MAAMC,MAAMC,MAAMC;AACtCuC,UAAMe,YAAY,EAAExD,MAAMC,MAAMC;AAChCuC,UAAMgB,YAAY,EAAExD,MAAMC;AAC1BuC,UAAMiB,YAAY,CAACxD;AACnBuC,UAAMO,OAAOA;AACbP,UAAMH,SAASA,OAAOO;AACtBJ,UAAME,QAAQA,MAAME;AACpBJ,UAAMS,SAASA;AACfT,UAAM9C,mBAAmBA;AACzB8C,UAAMD,WAAWA,SAASK;AAC1BJ,UAAM7C,aAAaA;EACrB,CAAC;AAED,MAAIe,YAAY;AACdC,WAAO+C,iBAAiB,UAAUf,YAAY;MAAEgB,SAAS;IAAK,CAAC;AAE/DC,mBAAe,MAAM;AACnBjD,aAAOkD,oBAAoB,UAAUlB,UAAU;IACjD,GAAG,IAAI;EACT;AAEA,SAAO;IAAE,GAAGmB,OAAOtB,KAAK;IAAGK;IAAQpC,KAAK,CAAC,CAACA;EAAI;AAChD;AAEO,IAAMsD,mBAAmBC,aAAa;EAC3Cf,QAAQ;IACNgB,MAAM3C;IACN4C,SAAS;EACX;EACAxE,kBAAkB,CAACyE,QAAQC,MAAM;AACnC,GAAG,SAAS;AAEL,SAASC,aAGd;AAAA,MAFAC,QAAmBlE,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAG;IAAE6C,QAAQ;EAAK;AAAC,MACtCF,OAAI3C,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAGmE,uBAAuB;AAE9B,QAAMC,UAAUC,OAAOnF,aAAa;AAEpC,MAAI,CAACkF,QAAS,OAAM,IAAIE,MAAM,0CAA0C;AAExE,QAAMzB,SAAS0B,SAAS,MAAM;AAC5B,QAAIL,MAAMrB,QAAQ;AAChB,aAAO;IACT,WAAW,OAAOqB,MAAM5E,qBAAqB,UAAU;AACrD,aAAO8E,QAAQ9B,MAAME,QAAQ0B,MAAM5E;IACrC,WAAW4E,MAAM5E,kBAAkB;AACjC,aAAO8E,QAAQ9B,MAAME,QAAQ4B,QAAQ7E,WAAWiD,MAAM0B,MAAM5E,gBAAgB;IAC9E,WAAW4E,MAAMrB,WAAW,MAAM;AAChC,aAAOuB,QAAQvB,OAAOL;IACxB,OAAO;AACL,aAAO;IACT;EACF,CAAC;AAED,QAAMgC,iBAAiBC,MAAM,MAAM;AACjC,QAAI,CAAC9B,KAAM,QAAO,CAAC;AAEnB,WAAO;MAAE,CAAC,GAAGA,IAAI,UAAU,GAAGE,OAAOL;IAAM;EAC7C,CAAC;AAED,SAAO;IAAE,GAAG4B;IAASI;IAAgB3B;EAAO;AAC9C;;;ACtJO,IAAM6B,cAA2CC,OAAOC,IAAI,eAAe;AAE3E,IAAMC,iBAAiBC,aAAa;EACzCC,OAAOC;AACT,GAAG,OAAO;AAEV,SAASC,eAAe;AACtB,SAAO;IACLC,cAAc;IACdC,YAAY;MAAEC,QAAQ,CAAA;MAAIC,SAAS;MAAGC,QAAQ;IAAE;IAChDC,QAAQ;MACNC,OAAO;QACLC,MAAM;QACNL,QAAQ;UACNM,YAAY;UACZC,SAAS;UACT,kBAAkB;UAClB,iBAAiB;UACjB,mBAAmB;UACnB,sBAAsB;UACtBC,SAAS;UACT,oBAAoB;UACpBC,WAAW;UACX,sBAAsB;UACtBC,OAAO;UACPC,MAAM;UACNC,SAAS;UACTC,SAAS;QACX;QACAC,WAAW;UACT,gBAAgB;UAChB,kBAAkB;UAClB,yBAAyB;UACzB,2BAA2B;UAC3B,oBAAoB;UACpB,gBAAgB;UAChB,iBAAiB;UACjB,iBAAiB;UACjB,oBAAoB;UACpB,qBAAqB;UACrB,mBAAmB;UACnB,mBAAmB;UACnB,aAAa;UACb,gBAAgB;UAChB,cAAc;UACd,iBAAiB;QACnB;MACF;MACAT,MAAM;QACJA,MAAM;QACNL,QAAQ;UACNM,YAAY;UACZC,SAAS;UACT,kBAAkB;UAClB,iBAAiB;UACjB,mBAAmB;UACnB,sBAAsB;UACtBC,SAAS;UACT,oBAAoB;UACpBC,WAAW;UACX,sBAAsB;UACtBC,OAAO;UACPC,MAAM;UACNC,SAAS;UACTC,SAAS;QACX;QACAC,WAAW;UACT,gBAAgB;UAChB,kBAAkB;UAClB,yBAAyB;UACzB,2BAA2B;UAC3B,oBAAoB;UACpB,gBAAgB;UAChB,iBAAiB;UACjB,iBAAiB;UACjB,oBAAoB;UACpB,qBAAqB;UACrB,mBAAmB;UACnB,mBAAmB;UACnB,aAAa;UACb,gBAAgB;UAChB,cAAc;UACd,iBAAiB;QACnB;MACF;IACF;IACAC,cAAc;EAChB;AACF;AAEA,SAASC,oBAAgF;AArMzF;AAqMyF,MAA7DC,UAAqBC,UAAAC,SAAA,KAAAD,UAAA,CAAA,MAAAE,SAAAF,UAAA,CAAA,IAAGrB,aAAY;AAC9D,QAAMwB,WAAWxB,aAAY;AAE7B,MAAI,CAACoB,QAAS,QAAO;IAAE,GAAGI;IAAUC,YAAY;EAAK;AAErD,QAAMnB,SAAkD,CAAC;AACzD,aAAW,CAACoB,KAAK5B,KAAK,KAAK6B,OAAOC,QAAQR,QAAQd,UAAU,CAAC,CAAC,GAAG;AAC/D,UAAML,eAAeH,MAAMU,QAAQkB,QAAQ,UACvCF,cAASlB,WAATkB,mBAAiBhB,QACjBgB,cAASlB,WAATkB,mBAAiBjB;AACrBD,WAAOoB,GAAG,IAAIG,UAAU5B,cAAcH,KAAK;EAC7C;AAEA,SAAO+B,UACLL,UACA;IAAE,GAAGJ;IAASd;EAAO,CACvB;AACF;AAEA,SAASwB,eAAgBC,OAAiBC,UAAkBC,SAAmBC,OAAgB;AAC7FH,QAAMI,KACJ,GAAGC,kBAAkBJ,UAAUE,KAAK,CAAC;GACrC,GAAGD,QAAQI,IAAIC,UAAQ,KAAKA,IAAI;CAAK,GACrC,KACF;AACF;AAEA,SAASC,gBAAiBzC,OAAgC;AACxD,QAAM0C,eAAe1C,MAAMU,OAAO,IAAI;AACtC,QAAMiC,cAAc3C,MAAMU,OAAO,IAAI;AAErC,QAAMS,YAAsB,CAAA;AAC5B,aAAW,CAACS,KAAKgB,KAAK,KAAKf,OAAOC,QAAQ9B,MAAMK,MAAM,GAAG;AACvD,UAAMwC,MAAMC,WAAWF,KAAK;AAC5BzB,cAAUkB,KAAK,aAAaT,GAAG,KAAKiB,IAAIE,CAAC,IAAIF,IAAIG,CAAC,IAAIH,IAAII,CAAC,EAAE;AAC7D,QAAI,CAACrB,IAAIsB,WAAW,KAAK,GAAG;AAC1B/B,gBAAUkB,KAAK,aAAaT,GAAG,wBAAwBuB,QAAQP,KAAK,IAAI,OAAOF,eAAeC,WAAW,EAAE;IAC7G;EACF;AAEA,aAAW,CAACf,KAAKgB,KAAK,KAAKf,OAAOC,QAAQ9B,MAAMmB,SAAS,GAAG;AAC1D,UAAMiC,QAAQ,OAAOR,UAAU,YAAYA,MAAMM,WAAW,GAAG,IAAIJ,WAAWF,KAAK,IAAInB;AACvF,UAAMoB,MAAMO,QAAQ,GAAGA,MAAML,CAAC,KAAKK,MAAMJ,CAAC,KAAKI,MAAMH,CAAC,KAAKxB;AAC3DN,cAAUkB,KAAK,OAAOT,GAAG,KAAKiB,OAAOD,KAAK,EAAE;EAC9C;AAEA,SAAOzB;AACT;AAEA,SAASkC,aAAcC,MAAcF,OAAehD,YAAuC;AACzF,QAAMmD,SAAiC,CAAC;AACxC,MAAInD,YAAY;AACd,eAAWoD,aAAc,CAAC,WAAW,QAAQ,GAAa;AACxD,YAAMC,KAAKD,cAAc,YAAYlD,UAAUC;AAC/C,iBAAWmD,UAAUC,YAAYvD,WAAWoD,SAAS,GAAG,CAAC,GAAG;AAC1DD,eAAO,GAAGD,IAAI,IAAIE,SAAS,IAAIE,MAAM,EAAE,IAAIE,SAASH,GAAGX,WAAWM,KAAK,GAAGM,MAAM,CAAC;MACnF;IACF;EACF;AACA,SAAOH;AACT;AAEA,SAASM,cAAexD,QAA2CD,YAAuC;AACxG,MAAI,CAACA,WAAY,QAAO,CAAC;AAEzB,MAAI0D,kBAAkB,CAAC;AACvB,aAAWR,QAAQlD,WAAWC,QAAQ;AACpC,UAAM+C,QAAQ/C,OAAOiD,IAAI;AAEzB,QAAI,CAACF,MAAO;AAEZU,sBAAkB;MAChB,GAAGA;MACH,GAAGT,aAAaC,MAAMF,OAAOhD,UAAU;IACzC;EACF;AACA,SAAO0D;AACT;AAEA,SAASC,YAAa1D,QAA2C;AAC/D,QAAM2D,WAAW,CAAC;AAElB,aAAWZ,SAASvB,OAAOoC,KAAK5D,MAAM,GAAG;AACvC,QAAI+C,MAAMF,WAAW,KAAK,KAAK7C,OAAO,MAAM+C,KAAK,EAAE,EAAG;AAEtD,UAAMc,UAAU,MAAMd,KAAK;AAC3B,UAAMe,WAAWrB,WAAWzC,OAAO+C,KAAK,CAAC;AAEzCY,aAASE,OAAO,IAAIE,cAAcD,QAAQ;EAC5C;AAEA,SAAOH;AACT;AAEA,SAAS1B,kBAAmBJ,UAAkBE,OAAgB;AAC5D,MAAI,CAACA,MAAO,QAAOF;AAEnB,QAAMmC,gBAAgB,UAAUjC,KAAK;AAErC,SAAOF,aAAa,UAAUmC,gBAAgB,GAAGA,aAAa,IAAInC,QAAQ;AAC5E;AAEA,SAASoC,aAAcC,SAAkCC,QAAgB;AACvE,MAAI,CAACD,QAAS;AAEdA,UAAQE,YAAYD;AACtB;AAEA,SAASE,wBAAyBC,IAAYC,UAAmB;AAC/D,MAAI,CAACC,WAAY,QAAO;AAExB,MAAIC,QAAQC,SAASC,eAAeL,EAAE;AAEtC,MAAI,CAACG,OAAO;AACVA,YAAQC,SAASE,cAAc,OAAO;AACtCH,UAAMH,KAAKA;AACXG,UAAMI,OAAO;AAEb,QAAIN,SAAUE,OAAMK,aAAa,SAASP,QAAQ;AAElDG,aAASK,KAAKC,YAAYP,KAAK;EACjC;AAEA,SAAOA;AACT;AAGO,SAASQ,YAAahE,SAAyE;AACpG,QAAMiE,gBAAgBlE,kBAAkBC,OAAO;AAC/C,QAAMgC,OAAOkC,WAAWD,cAAcpF,YAAY;AAClD,QAAMK,SAASiF,IAAIF,cAAc/E,MAAM;AAEvC,QAAMkF,iBAAiBC,SAAS,MAAM;AACpC,UAAMC,MAA+C,CAAC;AACtD,eAAW,CAACtC,OAAMuC,QAAQ,KAAKhE,OAAOC,QAAQtB,OAAOoC,KAAK,GAAG;AAC3D,YAAMvC,SAAS;QACb,GAAGwF,SAASxF;QACZ,GAAGwD,cAAcgC,SAASxF,QAAQkF,cAAcnF,UAAU;MAC5D;AAEAwF,UAAItC,KAAI,IAAI;QACV,GAAGuC;QACHxF,QAAQ;UACN,GAAGA;UACH,GAAG0D,YAAY1D,MAAM;QACvB;MACF;IACF;AACA,WAAOuF;EACT,CAAC;AAED,QAAME,UAAUC,MAAM,MAAML,eAAe9C,MAAMU,KAAKV,KAAK,CAAC;AAE5D,QAAM4B,SAASmB,SAAS,MAAM;AA9VhC;AA+VI,UAAM1D,QAAkB,CAAA;AAExB,SAAI6D,aAAQlD,UAARkD,mBAAepF,MAAM;AACvBsB,qBAAeC,OAAO,SAAS,CAAC,oBAAoB,GAAGsD,cAAcnD,KAAK;IAC5E;AAEAJ,mBAAeC,OAAO,SAASQ,gBAAgBqD,QAAQlD,KAAK,GAAG2C,cAAcnD,KAAK;AAElF,eAAW,CAAC4D,WAAWhG,KAAK,KAAK6B,OAAOC,QAAQ4D,eAAe9C,KAAK,GAAG;AACrEZ,qBAAeC,OAAO,aAAa+D,SAAS,IAAI,CAC9C,iBAAiBhG,MAAMU,OAAO,SAAS,QAAQ,IAC/C,GAAG+B,gBAAgBzC,KAAK,CAAC,GACxBuF,cAAcnD,KAAK;IACxB;AAEA,UAAM6D,UAAoB,CAAA;AAC1B,UAAMC,UAAoB,CAAA;AAE1B,UAAM7F,SAAS,IAAI8F,IAAItE,OAAOuE,OAAOV,eAAe9C,KAAK,EAAEyD,QAAQrG,WAAS6B,OAAOoC,KAAKjE,MAAMK,MAAM,CAAC,CAAC;AACtG,eAAWuB,OAAOvB,QAAQ;AACxB,UAAIuB,IAAIsB,WAAW,KAAK,GAAG;AACzBlB,uBAAekE,SAAS,IAAItE,GAAG,IAAI,CAAC,4BAA4BA,GAAG,eAAe,GAAG2D,cAAcnD,KAAK;MAC1G,OAAO;AACLJ,uBAAeiE,SAAS,OAAOrE,GAAG,IAAI,CACpC,+CAA+CA,GAAG,wBAClD,uCAAuCA,GAAG,iBAC1C,+BAA+BA,GAAG,eAAe,GAChD2D,cAAcnD,KAAK;AACtBJ,uBAAekE,SAAS,SAAStE,GAAG,IAAI,CAAC,4BAA4BA,GAAG,eAAe,GAAG2D,cAAcnD,KAAK;AAC7GJ,uBAAekE,SAAS,WAAWtE,GAAG,IAAI,CAAC,mCAAmCA,GAAG,GAAG,GAAG2D,cAAcnD,KAAK;MAC5G;IACF;AAEAH,UAAMI,KAAK,GAAG4D,SAAS,GAAGC,OAAO;AAEjC,WAAOjE,MAAMM,IAAI,CAAC+D,KAAKC,MAAMA,MAAM,IAAID,MAAM,OAAOA,GAAG,EAAE,EAAEE,KAAK,EAAE;EACpE,CAAC;AAED,WAASC,QAASC,KAAU;AAC1B,QAAInB,cAAc5D,WAAY;AAE9B,UAAMyD,OAAOsB,IAAIC,SAASC,SAASC;AACnC,QAAIzB,MAAM;AACR,UAAS0B,UAAT,WAAoB;AAClB,eAAO;UACLhC,OAAO,CAAC;YACNiC,aAAavC,OAAO5B;YACpB+B,IAAIY,cAAcnE;YAClB4F,OAAOzB,cAAcX,YAAY;UACnC,CAAC;QACH;MACF;AAEA,UAAIQ,KAAK/C,MAAM;AACb,cAAM4E,QAAQ7B,KAAK/C,KAAKyE,OAAO;AAC/B,YAAIjC,YAAY;AACdqC,gBAAM1C,QAAQ,MAAM;AAAEyC,kBAAME,MAAML,OAAO;UAAE,CAAC;QAC9C;MACF,OAAO;AACL,YAAIjC,YAAY;AACdO,eAAKgC,YAAYrB,MAAMe,OAAO,CAAC;AAC/BO,sBAAY,MAAMjC,KAAKkC,UAAU,CAAC;QACpC,OAAO;AACLlC,eAAKgC,YAAYN,QAAQ,CAAC;QAC5B;MACF;IACF,OAAO;AAOL,UAASS,eAAT,WAAyB;AACvBjD,qBACEI,wBAAwBa,cAAcnE,cAAcmE,cAAcX,QAAQ,GAC1EJ,OAAO5B,KACT;MACF;AAXA,UAAIiC,YAAY;AACdqC,cAAM1C,QAAQ+C,cAAc;UAAEC,WAAW;QAAK,CAAC;MACjD,OAAO;AACLD,qBAAa;MACf;IAQF;EACF;AAEA,QAAME,eAAe1B,MAAM,MAAMR,cAAc5D,aAAaF,SAAY,YAAY6B,KAAKV,KAAK,EAAE;AAEhG,SAAO;IACL6D;IACA9E,YAAY4D,cAAc5D;IAC1B2B;IACA9C;IACAsF;IACAJ;IACA+B;IACAjD;IACAkD,QAAQ;MACNpE;MACAwC;IACF;EACF;AACF;AAEO,SAAS6B,aAAcC,OAA2B;AACvDC,qBAAmB,cAAc;AAEjC,QAAM7H,QAAQ8H,OAAOnI,aAAa,IAAI;AAEtC,MAAI,CAACK,MAAO,OAAM,IAAI+H,MAAM,wCAAwC;AAEpE,QAAMzE,OAAOyC,MAAM,MAAM6B,MAAM5H,SAASA,MAAMsD,KAAKV,KAAK;AACxD,QAAMkD,UAAUC,MAAM,MAAM/F,MAAMQ,OAAOoC,MAAMU,KAAKV,KAAK,CAAC;AAE1D,QAAM6E,eAAe1B,MAAM,MAAM/F,MAAM2B,aAAaF,SAAY,YAAY6B,KAAKV,KAAK,EAAE;AAExF,QAAMoF,WAA0B;IAC9B,GAAGhI;IACHsD;IACAwC;IACA2B;EACF;AAEAQ,UAAQtI,aAAaqI,QAAQ;AAE7B,SAAOA;AACT;AAEO,SAASE,WAAY;AAC1BL,qBAAmB,UAAU;AAE7B,QAAM7H,QAAQ8H,OAAOnI,aAAa,IAAI;AAEtC,MAAI,CAACK,MAAO,OAAM,IAAI+H,MAAM,wCAAwC;AAEpE,SAAO/H;AACT;", "names": ["useToggleScope", "source", "fn", "scope", "start", "effectScope", "run", "length", "stop", "watch", "active", "undefined", "immediate", "onScopeDispose", "useProxiedModel", "props", "prop", "defaultValue", "transformIn", "arguments", "length", "undefined", "v", "transformOut", "vm", "getCurrentInstance", "internal", "ref", "kebabProp", "toKebabCase", "checkKebab", "isControlled", "computed", "vnode", "hasOwnProperty", "useToggleScope", "value", "watch", "val", "model", "get", "externalValue", "set", "internalValue", "newValue", "toRaw", "emit", "Object", "defineProperty", "badge", "open", "close", "dismiss", "confirmEdit", "ok", "cancel", "dataIterator", "noResultsText", "loadingText", "dataTable", "itemsPerPageText", "aria<PERSON><PERSON><PERSON>", "sortDescending", "sortAscending", "sortNone", "activateNone", "activateDescending", "activateAscending", "sortBy", "dataFooter", "itemsPerPageAll", "nextPage", "prevPage", "firstPage", "lastPage", "pageText", "dateRangeInput", "divider", "datePicker", "itemsSelected", "range", "title", "header", "input", "placeholder", "noDataText", "carousel", "prev", "next", "delimiter", "calendar", "moreEvents", "today", "clear", "prependAction", "appendAction", "otp", "fileInput", "counter", "counterSize", "fileUpload", "browse", "timePicker", "am", "pm", "pagination", "root", "previous", "page", "currentPage", "first", "last", "stepper", "rating", "item", "loading", "infiniteScroll", "loadMore", "empty", "rules", "required", "email", "number", "integer", "capital", "max<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "strictLength", "exclude", "notEmpty", "pattern", "LANG_PREFIX", "replace", "str", "params", "match", "index", "String", "Number", "createTranslateFunction", "current", "fallback", "messages", "key", "_len", "arguments", "length", "Array", "_key", "startsWith", "<PERSON><PERSON><PERSON>", "currentLocale", "value", "fallback<PERSON><PERSON><PERSON>", "getObjectValueByPath", "console<PERSON>arn", "consoleError", "createNumberFunction", "options", "numberFormat", "Intl", "NumberFormat", "format", "useProvided", "props", "prop", "provided", "internal", "useProxiedModel", "watch", "v", "createProvideFunction", "state", "name", "t", "n", "provide", "createVuetifyAdapter", "shallowRef", "locale", "ref", "en", "LocaleSymbol", "Symbol", "for", "isLocaleInstance", "obj", "name", "createLocale", "options", "i18n", "adapter", "createVuetifyAdapter", "rtl", "createRtl", "useLocale", "locale", "inject", "Error", "provideLocale", "props", "provide", "provideRtl", "data", "RtlSymbol", "gen<PERSON><PERSON><PERSON><PERSON>", "af", "ar", "bg", "ca", "ckb", "cs", "de", "el", "en", "es", "et", "fa", "fi", "fr", "hr", "hu", "he", "id", "it", "ja", "km", "ko", "lv", "lt", "nl", "no", "pl", "pt", "ro", "ru", "sk", "sl", "srCyrl", "srLatn", "sv", "th", "tr", "az", "uk", "vi", "zhHans", "zhHant", "ref", "isRtl", "computed", "value", "current", "rtlClasses", "toRef", "useRtl", "breakpoints", "DisplaySymbol", "Symbol", "for", "defaultDisplayOptions", "mobileBreakpoint", "thresholds", "xs", "sm", "md", "lg", "xl", "xxl", "parseDisplayOptions", "options", "arguments", "length", "undefined", "mergeDeep", "getClientWidth", "ssr", "IN_BROWSER", "window", "innerWidth", "clientWidth", "getClientHeight", "innerHeight", "clientHeight", "getPlatform", "userAgent", "navigator", "match", "regexp", "Boolean", "android", "ios", "<PERSON><PERSON>", "electron", "chrome", "edge", "firefox", "opera", "win", "mac", "linux", "touch", "SUPPORTS_TOUCH", "createDisplay", "height", "shallowRef", "platform", "state", "reactive", "width", "updateSize", "value", "update", "watchEffect", "name", "breakpoint<PERSON><PERSON>ue", "mobile", "smAndUp", "mdAndUp", "lgAndUp", "xlAndUp", "smAndDown", "mdAndDown", "lgAndDown", "xlAndDown", "addEventListener", "passive", "onScopeDispose", "removeEventListener", "toRefs", "makeDisplayProps", "propsFactory", "type", "default", "Number", "String", "useDisplay", "props", "getCurrentInstanceName", "display", "inject", "Error", "computed", "displayClasses", "toRef", "ThemeSymbol", "Symbol", "for", "makeThemeProps", "propsFactory", "theme", "String", "gen<PERSON><PERSON><PERSON><PERSON>", "defaultTheme", "variations", "colors", "lighten", "darken", "themes", "light", "dark", "background", "surface", "primary", "secondary", "error", "info", "success", "warning", "variables", "stylesheetId", "parseThemeOptions", "options", "arguments", "length", "undefined", "defaults", "isDisabled", "key", "Object", "entries", "mergeDeep", "createCssClass", "lines", "selector", "content", "scope", "push", "getScopedSelector", "map", "line", "genCssVariables", "lightOverlay", "dark<PERSON><PERSON><PERSON>", "value", "rgb", "parseColor", "r", "g", "b", "startsWith", "getLuma", "color", "genVariation", "name", "object", "variation", "fn", "amount", "createRange", "RGBtoHex", "genVariations", "variationColors", "genOnColors", "onColors", "keys", "onColor", "colorVal", "getForeground", "scopeSelector", "upsertStyles", "styleEl", "styles", "innerHTML", "getOrCreateStyleElement", "id", "cspNonce", "IN_BROWSER", "style", "document", "getElementById", "createElement", "type", "setAttribute", "head", "append<PERSON><PERSON><PERSON>", "createTheme", "parsedOptions", "shallowRef", "ref", "computedThemes", "computed", "acc", "original", "current", "toRef", "themeName", "bgLines", "fgLines", "Set", "values", "flatMap", "str", "i", "join", "install", "app", "_context", "provides", "usehead", "getHead", "textContent", "nonce", "entry", "watch", "patch", "addHeadObjs", "watchEffect", "updateDOM", "updateStyles", "immediate", "themeClasses", "global", "provideTheme", "props", "getCurrentInstance", "inject", "Error", "newTheme", "provide", "useTheme"]}