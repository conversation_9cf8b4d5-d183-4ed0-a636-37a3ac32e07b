/* Global styles */
html, body {
  margin: 0;
  padding: 0;
  font-family: 'Roboto', sans-serif;
}

.text-primary {
  color: #1976D2 !important;
}

.text-grey {
  color: #757575 !important;
}

/* Custom utility classes */
.pa-0 {
  padding: 0 !important;
}

.ma-0 {
  margin: 0 !important;
}

/* Global Table Styles - 全局表格样式 */
/* 为所有v-data-table添加边框和蓝色表头背景 */

/* 表格整体边框 */
.v-data-table {
  border: 1px solid #e0e0e0 !important;
  border-radius: 4px !important;
}

/* 表头样式 - 蓝色背景 */
.v-data-table .v-data-table__thead {
  background-color: #1976d2 !important;
}

.v-data-table .v-data-table__thead .v-data-table__tr {
  background-color: #1976d2 !important;
}

.v-data-table .v-data-table__thead .v-data-table__th {
  background-color: #1976d2 !important;
  color: white !important;
  font-weight: 600 !important;
  border-bottom: 1px solid #1565c0 !important;
  border-right: 1px solid #1565c0 !important;
}

.v-data-table .v-data-table__thead .v-data-table__th:last-child {
  border-right: none !important;
}

/* 表体边框 */
.v-data-table .v-data-table__tbody .v-data-table__tr {
  border-bottom: 1px solid #e0e0e0 !important;
}

.v-data-table .v-data-table__tbody .v-data-table__td {
  border-right: 1px solid #e0e0e0 !important;
  padding: 12px 16px !important;
}

.v-data-table .v-data-table__tbody .v-data-table__td:last-child {
  border-right: none !important;
}

/* 表格悬停效果 */
.v-data-table .v-data-table__tbody .v-data-table__tr:hover .v-data-table__td {
  background-color: #f5f5f5 !important;
}

/* 确保表头文字可见性 */
.v-data-table .v-data-table__thead .v-data-table__th .v-data-table-header__content {
  color: white !important;
}

.v-data-table .v-data-table__thead .v-data-table__th .v-data-table-header__content .v-icon {
  color: white !important;
}

/* 确保排序图标也是白色 */
.v-data-table .v-data-table__thead .v-data-table__th .v-data-table-header__sort-icon {
  color: white !important;
}

/* bordered-table类的额外样式 */
.bordered-table {
  box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}