<template>
  <div class="release-summary">
    <div class="mb-4">
      <h2 class="text-h4 mb-2">Release Summary</h2>
    </div>

    <!-- Summary Cards -->
    <v-row class="mb-6 summary-cards">
      <v-col cols="12" sm="6" lg="auto" class="summary-card-col">
        <v-card class="text-center summary-card" color="blue-lighten-5">
          <v-card-text class="pa-3">
            <v-icon size="24" color="blue">mdi-rocket-launch</v-icon>
            <div class="text-h5 font-weight-bold text-blue mt-1">{{ totalReleases }}</div>
            <div class="text-caption text-grey-darken-1">Total Releases</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" lg="auto" class="summary-card-col">
        <v-card class="text-center summary-card" color="green-lighten-5">
          <v-card-text class="pa-3">
            <v-icon size="24" color="green">mdi-check-circle</v-icon>
            <div class="text-h5 font-weight-bold text-green mt-1">{{ completedReleases }}</div>
            <div class="text-caption text-grey-darken-1">Completed</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" lg="auto" class="summary-card-col">
        <v-card class="text-center summary-card" color="orange-lighten-5">
          <v-card-text class="pa-3">
            <v-icon size="24" color="orange">mdi-progress-clock</v-icon>
            <div class="text-h5 font-weight-bold text-orange mt-1">{{ inProgressReleases }}</div>
            <div class="text-caption text-grey-darken-1">In Progress</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" lg="auto" class="summary-card-col">
        <v-card class="text-center summary-card" color="grey-lighten-4">
          <v-card-text class="pa-3">
            <v-icon size="24" color="grey">mdi-clock-outline</v-icon>
            <div class="text-h5 font-weight-bold text-grey-darken-2 mt-1">
              {{ planningReleases }}
            </div>
            <div class="text-caption text-grey-darken-1">Planning</div>
          </v-card-text>
        </v-card>
      </v-col>

      <v-col cols="12" sm="6" lg="auto" class="summary-card-col">
        <v-card class="text-center summary-card" color="red-lighten-5">
          <v-card-text class="pa-3">
            <v-icon size="24" color="red">mdi-alert-circle</v-icon>
            <div class="text-h5 font-weight-bold text-red mt-1">{{ highRiskReleases }}</div>
            <div class="text-caption text-grey-darken-1">High Risk</div>
          </v-card-text>
        </v-card>
      </v-col>
    </v-row>

    <!-- Release Table -->
    <v-card class="mt-6">
      <v-card-title class="d-flex justify-space-between align-center">
        <span>All Releases</span>
        <v-btn color="primary" @click="$emit('createRelease')">
          <v-icon>mdi-plus</v-icon>
          New Release
        </v-btn>
      </v-card-title>

      <!-- 字段分组说明 -->
      <v-card-subtitle class="group-legend">
        <div
          class="d-flex flex-wrap gap-2 pa-3"
          style="background-color: #f8f9fa; border-radius: 6px; border: 1px solid #e0e0e0"
        >
          <v-chip size="small" color="blue-grey" variant="flat" style="color: white"
            >基本信息</v-chip
          >
          <v-chip size="small" color="primary" variant="flat">MT0 - PRD阶段</v-chip>
          <v-chip size="small" color="purple" variant="flat">MT1 - 测试策略阶段</v-chip>
          <v-chip size="small" color="orange" variant="flat">MT2 - 代码冻结阶段</v-chip>
          <v-chip size="small" color="green" variant="flat">MT3 - 发布阶段</v-chip>
          <v-chip size="small" color="light-blue" variant="flat">MT4 - 总结阶段</v-chip>
        </div>
      </v-card-subtitle>

      <!-- 表格容器 -->
      <div class="table-responsive">
        <v-data-table
          :headers="groupedHeaders"
          :items="releases"
          :loading="loading"
          item-value="id"
          class="elevation-0 grouped-table"
          @click:row="(_, { item }) => $emit('selectRelease', item)"
          hover
          :items-per-page="10"
          :items-per-page-options="[5, 10, 25, -1]"
          fixed-header
          height="600px"
        >
          <template v-slot:item.state="{ item }">
            <v-chip :color="getStatusColor(item.state)" size="small" variant="flat">
              {{ item.state || 'No Status' }}
            </v-chip>
          </template>

          <template v-slot:item.risk_state="{ item }">
            <v-chip :color="getRiskColor(item.risk_state)" size="small" variant="outlined">
              {{ item.risk_state || 'Unknown' }}
            </v-chip>
          </template>

          <!-- 新增的状态字段模板 -->
          <template v-slot:item.prd_signoff_status="{ item }">
            <v-chip :color="getPhaseStatusColor(item.prd_signoff_status)" size="small" variant="flat">
              {{ item.prd_signoff_status || 'Not Start' }}
            </v-chip>
          </template>

          <template v-slot:item.branchoff_status="{ item }">
            <v-chip :color="getPhaseStatusColor(item.branchoff_status)" size="small" variant="flat">
              {{ item.branchoff_status || 'Not Start' }}
            </v-chip>
          </template>

          <template v-slot:item.code_freeze_status="{ item }">
            <v-chip :color="getPhaseStatusColor(item.code_freeze_status)" size="small" variant="flat">
              {{ item.code_freeze_status || 'Not Start' }}
            </v-chip>
          </template>

          <template v-slot:item.prd_signoff="{ item }">
            {{ formatDate(item.prd_signoff) }}
          </template>

          <template v-slot:item.test_strategy_signoff="{ item }">
            {{ formatDate(item.test_strategy_signoff) }}
          </template>

          <template v-slot:item.release_branch_off="{ item }">
            {{ formatDate(item.release_branch_off) }}
          </template>

          <template v-slot:item.release_code_freeze="{ item }">
            {{ formatDate(item.release_code_freeze) }}
          </template>

          <template v-slot:item.created_at="{ item }">
            {{ formatDateTime(item.created_at) }}
          </template>

          <template v-slot:item.updated_at="{ item }">
            {{ formatDateTime(item.updated_at) }}
          </template>

          <template v-slot:item.prd_link="{ item }">
            <v-btn
              v-if="item.prd_link"
              icon="mdi-link"
              size="small"
              variant="text"
              color="primary"
              @click.stop="openLink(item.prd_link)"
            ></v-btn>
            <span v-else class="text-grey">-</span>
          </template>

          <template v-slot:item.test_strategy_link="{ item }">
            <v-btn
              v-if="item.test_strategy_link"
              icon="mdi-link"
              size="small"
              variant="text"
              color="primary"
              @click.stop="openLink(item.test_strategy_link)"
            ></v-btn>
            <span v-else class="text-grey">-</span>
          </template>

          <template v-slot:item.risk_link="{ item }">
            <v-btn
              v-if="item.risk_link"
              icon="mdi-link"
              size="small"
              variant="text"
              color="primary"
              @click.stop="openLink(item.risk_link)"
            ></v-btn>
            <span v-else-if="item.risk" class="text-grey">Text</span>
            <span v-else class="text-grey">-</span>
          </template>

          <template v-slot:item.software_download="{ item }">
            <v-btn
              v-if="item.software_download"
              icon="mdi-download"
              size="small"
              variant="text"
              color="success"
              @click.stop="openLink(item.software_download)"
            ></v-btn>
            <span v-else-if="item.softwares" class="text-grey">Text</span>
            <span v-else class="text-grey">-</span>
          </template>

          <template v-slot:item.doc_link="{ item }">
            <v-btn
              v-if="item.doc_link"
              icon="mdi-file-document"
              size="small"
              variant="text"
              color="info"
              @click.stop="openLink(item.doc_link)"
            ></v-btn>
            <span v-else-if="item.docs" class="text-grey">Text</span>
            <span v-else class="text-grey">-</span>
          </template>

          <template v-slot:item.risk="{ item }">
            <div v-if="item.risk || item.risk_link" class="text-truncate" style="max-width: 150px">
              <v-tooltip activator="parent" location="top">
                {{ item.risk || 'Link available' }}
              </v-tooltip>
              {{ truncateText(item.risk || 'Link', 20) }}
            </div>
            <span v-else class="text-grey">-</span>
          </template>

          <template v-slot:item.softwares="{ item }">
            <div
              v-if="item.softwares || item.software_download"
              class="text-truncate"
              style="max-width: 120px"
            >
              <v-tooltip activator="parent" location="top">
                {{ item.softwares || 'Download link available' }}
              </v-tooltip>
              {{ truncateText(item.softwares || 'Link', 15) }}
            </div>
            <span v-else class="text-grey">-</span>
          </template>

          <template v-slot:item.docs="{ item }">
            <div v-if="item.docs || item.doc_link" class="text-truncate" style="max-width: 120px">
              <v-tooltip activator="parent" location="top">
                {{ item.docs || 'Doc link available' }}
              </v-tooltip>
              {{ truncateText(item.docs || 'Link', 15) }}
            </div>
            <span v-else class="text-grey">-</span>
          </template>

          <template v-slot:item.test_report="{ item }">
            <div v-if="item.test_report" class="text-truncate" style="max-width: 120px">
              <v-tooltip activator="parent" location="top">
                {{ item.test_report }}
              </v-tooltip>
              {{ truncateText(item.test_report, 15) }}
            </div>
            <span v-else class="text-grey">-</span>
          </template>

          <template v-slot:item.lessons="{ item }">
            <div v-if="item.lessons" class="text-truncate" style="max-width: 150px">
              <v-tooltip activator="parent" location="top">
                {{ item.lessons }}
              </v-tooltip>
              {{ truncateText(item.lessons, 20) }}
            </div>
            <span v-else class="text-grey">-</span>
          </template>

          <template v-slot:item.actions="{ item }">
            <v-btn icon size="small" variant="text" @click.stop="$emit('editRelease', item)">
              <v-icon>mdi-pencil</v-icon>
            </v-btn>
            <v-btn
              icon
              size="small"
              variant="text"
              color="error"
              @click.stop="$emit('deleteRelease', item)"
            >
              <v-icon>mdi-delete</v-icon>
            </v-btn>
          </template>

          <template v-slot:no-data>
            <div class="text-center pa-8">
              <v-icon size="64" color="grey">mdi-rocket-launch-outline</v-icon>
              <div class="text-h6 mt-4 text-grey">No releases found</div>
              <p class="text-grey">Create your first release to get started</p>
            </div>
          </template>
        </v-data-table>
      </div>
    </v-card>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  releases: {
    type: Array,
    default: () => [],
  },
  loading: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['selectRelease', 'createRelease', 'editRelease', 'deleteRelease'])

const groupedHeaders = [
  // 基本信息列（不分组）
  { title: 'Release Name', key: 'name', sortable: true, width: '150px' },

  // MT0分组
  {
    title: 'MT0',
    key: 'mt0-group',
    align: 'center',
    children: [
      { title: 'PRD Signoff', key: 'prd_signoff', sortable: true, width: '120px', align: 'center' },
      { title: 'PRD Signoff Status', key: 'prd_signoff_status', sortable: true, width: '120px', align: 'center' },
      { title: 'PRD Link', key: 'prd_link', sortable: false, width: '80px', align: 'center' },
    ],
  },

  // MT1分组
  {
    title: 'MT1',
    key: 'mt1-group',
    align: 'center',
    children: [
      {
        title: 'Test Strategy Signoff',
        key: 'test_strategy_signoff',
        sortable: true,
        width: '120px',
        align: 'center',
      },
      { title: 'Test Strategy Link', key: 'test_strategy_link', sortable: false, width: '80px', align: 'center' },
      { title: 'Release Branch Off', key: 'release_branch_off', sortable: true, width: '120px', align: 'center' },
      { title: 'Branchoff Status', key: 'branchoff_status', sortable: true, width: '120px', align: 'center' },
    ],
  },

  // MT2分组
  {
    title: 'MT2',
    key: 'mt2-group',
    align: 'center',
    children: [
      { title: 'Release Code Freeze', key: 'release_code_freeze', sortable: true, width: '120px', align: 'center' },
      { title: 'Code Freeze Status', key: 'code_freeze_status', sortable: true, width: '120px', align: 'center' },
    ],
  },

  // MT3分组
  {
    title: 'MT3',
    key: 'mt3-group',
    align: 'center',
    children: [
      { title: 'Release State', key: 'state', sortable: true, width: '120px', align: 'center' },
      { title: 'Release Test Report', key: 'test_report', sortable: true, width: '120px', align: 'center' },
      { title: 'Risk Link', key: 'risk_link', sortable: false, width: '80px', align: 'center' },
      { title: 'Risk State', key: 'risk_state', sortable: true, width: '110px', align: 'center' },
      { title: 'Software Download Link', key: 'software_download', sortable: false, width: '80px', align: 'center' },
      { title: 'Doc Link', key: 'doc_link', sortable: false, width: '80px', align: 'center' },
    ],
  },

  // MT4分组
  {
    title: 'MT4',
    key: 'mt4-group',
    align: 'center',
    children: [{ title: 'Lessons Learnt', key: 'lessons', sortable: true, width: '150px', align: 'center' }],
  },

  // 其他字段
  { title: 'Actions', key: 'actions', sortable: false, width: '100px' },
]

const totalReleases = computed(() => props.releases.length)

const completedReleases = computed(
  () =>
    props.releases.filter(
      (r) => r.state?.toLowerCase() === 'completed' || r.state?.toLowerCase() === 'released'
    ).length
)

const inProgressReleases = computed(
  () =>
    props.releases.filter((r) =>
      ['development', 'testing', 'staging'].includes(r.state?.toLowerCase())
    ).length
)

const planningReleases = computed(
  () => props.releases.filter((r) => r.state?.toLowerCase() === 'planning' || !r.state).length
)

const highRiskReleases = computed(
  () =>
    props.releases.filter((r) => ['high', 'critical'].includes(r.risk_state?.toLowerCase())).length
)

function getStatusColor(status) {
  const statusColors = {
    planning: 'blue',
    development: 'orange',
    testing: 'purple',
    staging: 'cyan',
    released: 'green',
    completed: 'teal',
    cancelled: 'red',
  }
  return statusColors[status?.toLowerCase()] || 'grey'
}

function formatDate(dateString) {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    })
  } catch {
    return '-'
  }
}

function formatDateTime(dateString) {
  if (!dateString) return '-'
  try {
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    })
  } catch {
    return '-'
  }
}

function getRiskColor(riskState) {
  const riskColors = {
    low: 'green',
    medium: 'orange',
    high: 'red',
    critical: 'red-darken-2',
    monitored: 'blue',
    mitigated: 'teal',
    closed: 'grey',
  }
  return riskColors[riskState?.toLowerCase()] || 'grey'
}

function getPhaseStatusColor(status) {
  const statusColors = {
    'not start': 'grey',
    'na': 'blue-grey',
    'ongoing': 'orange',
    'done': 'green',
  }
  return statusColors[status?.toLowerCase()] || 'grey'
}

function truncateText(text, maxLength) {
  if (!text) return ''
  return text.length > maxLength ? text.substring(0, maxLength) + '...' : text
}

function openLink(url) {
  if (url) {
    window.open(url, '_blank')
  }
}
</script>

<style scoped>
.release-summary {
  max-width: 100%;
  padding: 0 16px;
}

/* Summary Cards 优化 */
.summary-cards {
  margin: 0 !important;
  justify-content: stretch !important;
  gap: 16px !important;
}

.summary-card-col {
  padding: 4px !important;
  max-width: none;
  flex: 1 1 0%;
}

.summary-card {
  height: 100%;
  min-height: 80px;
}

.summary-card .v-card-text {
  padding: 8px 12px !important;
}

/* 自适应表格容器 */
.table-responsive {
  width: 100%;
  overflow-x: auto;
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 transparent;
}

/* 只在必要时显示滚动条 */
.table-responsive::-webkit-scrollbar {
  height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
  background: transparent;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.v-data-table tbody tr {
  cursor: pointer;
}

.v-data-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* 自适应表格宽度 */
.v-data-table {
  width: 100%;
  min-width: fit-content;
}

.text-truncate {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* 保持默认表格样式，只自定义表头 */

/* 使用全局表头样式，无需特殊定制 */

/* MT分组表头和内容居中对齐 */
.grouped-table :deep(.v-data-table__th) {
  text-align: center !important;
}

.grouped-table :deep(.v-data-table__td) {
  text-align: center !important;
}

.grouped-table :deep(.v-data-table-header__content) {
  justify-content: center !important;
}

/* 保持默认表格样式 */

/* 分组标识chips */
.gap-2 {
  gap: 0.5rem;
}

.group-legend {
  margin-bottom: 8px;
}

.group-legend .v-chip {
  font-weight: 600;
  margin: 2px;
}
</style>
