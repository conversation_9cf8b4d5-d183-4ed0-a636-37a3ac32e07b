<template>
  <div class="pa-4">
    <v-card>
      <v-card-title class="d-flex align-center">
        <v-icon class="mr-2">mdi-rocket-launch</v-icon>
        <span>Release Overview</span>
      </v-card-title>

      <!-- 表格容器 -->
      <div class="table-responsive">
        <v-data-table
          :headers="groupedHeaders"
          :items="[release]"
          class="grouped-table elevation-1"
          fixed-header
          height="400px"
        >

          <!-- 状态字段模板 -->
          <template v-slot:item.state="{ item }">
            <v-chip :color="getStatusColor(item.state)" size="small" variant="flat">
              {{ item.state || 'No Status' }}
            </v-chip>
          </template>

          <template v-slot:item.risk_state="{ item }">
            <v-chip :color="getRiskColor(item.risk_state)" size="small" variant="outlined">
              {{ item.risk_state || 'Unknown' }}
            </v-chip>
          </template>

          <!-- 日期字段模板 -->
          <template v-slot:item.prd_signoff="{ item }">
            {{ formatDate(item.prd_signoff) }}
          </template>

          <template v-slot:item.test_strategy_signoff="{ item }">
            {{ formatDate(item.test_strategy_signoff) }}
          </template>

          <template v-slot:item.release_branch_off="{ item }">
            {{ formatDate(item.release_branch_off) }}
          </template>

          <template v-slot:item.release_code_freeze="{ item }">
            {{ formatDate(item.release_code_freeze) }}
          </template>

          <!-- 链接字段模板 -->
          <template v-slot:item.prd_link="{ item }">
            <a v-if="item.prd_link" :href="item.prd_link" target="_blank" class="text-decoration-none">
              <v-icon size="small">mdi-link</v-icon>
            </a>
            <span v-else>-</span>
          </template>

          <template v-slot:item.risk_link="{ item }">
            <a v-if="item.risk_link" :href="item.risk_link" target="_blank" class="text-decoration-none">
              <v-icon size="small">mdi-link</v-icon>
            </a>
            <span v-else>-</span>
          </template>

          <template v-slot:item.software_download="{ item }">
            <a v-if="item.software_download" :href="item.software_download" target="_blank" class="text-decoration-none">
              <v-icon size="small">mdi-download</v-icon>
            </a>
            <span v-else>-</span>
          </template>

          <template v-slot:item.doc_link="{ item }">
            <a v-if="item.doc_link" :href="item.doc_link" target="_blank" class="text-decoration-none">
              <v-icon size="small">mdi-file-document</v-icon>
            </a>
            <span v-else>-</span>
          </template>

          <!-- 文本字段模板 -->
          <template v-slot:item.lessons="{ item }">
            <div style="max-width: 200px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis">
              {{ item.lessons || '-' }}
            </div>
          </template>
        </v-data-table>
      </div>
    </v-card>
  </div>


</template>

<script setup>
import { defineProps } from 'vue'

const props = defineProps({
  release: {
    type: Object,
    required: true,
  },
})

const groupedHeaders = [
  // 基本信息列（不分组）
  { title: 'Release Name', key: 'name', sortable: true, width: '150px' },

  // MT0分组
  {
    title: 'MT0',
    key: 'mt0-group',
    align: 'center',
    children: [
      { title: 'PRD Signoff', key: 'prd_signoff', sortable: true, width: '120px', align: 'center' },
      { title: 'PRD Link', key: 'prd_link', sortable: false, width: '80px', align: 'center' },
    ],
  },

  // MT1分组
  {
    title: 'MT1',
    key: 'mt1-group',
    align: 'center',
    children: [
      {
        title: 'Test Strategy Signoff',
        key: 'test_strategy_signoff',
        sortable: true,
        width: '120px',
        align: 'center',
      },
      {
        title: 'Release Branch Off',
        key: 'release_branch_off',
        sortable: true,
        width: '120px',
        align: 'center',
      },
    ],
  },

  // MT2分组
  {
    title: 'MT2',
    key: 'mt2-group',
    align: 'center',
    children: [
      {
        title: 'Release Code Freeze',
        key: 'release_code_freeze',
        sortable: true,
        width: '120px',
        align: 'center',
      },
    ],
  },

  // MT3分组
  {
    title: 'MT3',
    key: 'mt3-group',
    align: 'center',
    children: [
      { title: 'Release State', key: 'state', sortable: true, width: '120px', align: 'center' },
      { title: 'Risk Link', key: 'risk_link', sortable: false, width: '80px', align: 'center' },
      { title: 'Risk State', key: 'risk_state', sortable: true, width: '110px', align: 'center' },
      {
        title: 'Software Download Link',
        key: 'software_download',
        sortable: false,
        width: '80px',
        align: 'center',
      },
      { title: 'Doc Link', key: 'doc_link', sortable: false, width: '80px', align: 'center' },
    ],
  },

  // MT4分组
  {
    title: 'MT4',
    key: 'mt4-group',
    align: 'center',
    children: [
      { title: 'Lessons Learnt', key: 'lessons', sortable: true, width: '150px', align: 'center' },
    ],
  },
]

function formatDate(dateString) {
  if (!dateString) return null
  return new Date(dateString).toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  })
}

function getStatusColor(status) {
  const statusColors = {
    planning: 'blue',
    development: 'orange',
    testing: 'purple',
    staging: 'cyan',
    release: 'green',
    completed: 'teal',
    cancelled: 'red',
  }
  return statusColors[status?.toLowerCase()] || 'grey'
}

function getRiskColor(riskState) {
  const riskColors = {
    low: 'green',
    medium: 'orange',
    high: 'red',
    critical: 'red-darken-2',
  }
  return riskColors[riskState?.toLowerCase()] || 'grey'
}
</script>

<style scoped>
/* 表格容器样式 */
.table-responsive {
  overflow-x: auto;
  max-width: 100%;
}

/* 只在必要时显示滚动条 */
.table-responsive::-webkit-scrollbar {
  height: 6px;
}

.table-responsive::-webkit-scrollbar-track {
  background: transparent;
}

.table-responsive::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

.v-data-table tbody tr {
  cursor: pointer;
}

.v-data-table tbody tr:hover {
  background-color: rgba(0, 0, 0, 0.04);
}

/* 自适应表格宽度 */
.v-data-table {
  width: 100%;
  min-width: fit-content;
}

/* 表头居中对齐 */
.grouped-table :deep(.v-data-table-header__content) {
  justify-content: center !important;
}

/* 保持默认表格样式，无自定义分隔线 */

/* 保持简洁的表格样式 */
.grouped-table :deep(.v-data-table__th) {
  background-color: #f8f9fa !important;
  font-weight: 600 !important;
  color: #495057 !important;
}

.grouped-table :deep(.v-data-table__td) {
  padding: 8px 12px !important;
}
</style>
