[GIN-debug] [WARNING] Creating an Engine instance with the Logger and Recovery middleware already attached.

[GIN-debug] [WARNING] Running in "debug" mode. Switch to "release" mode in production.
 - using env:	export GIN_MODE=release
 - using code:	gin.SetMode(gin.ReleaseMode)

[GIN-debug] GET    /uploads/*filepath        --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (4 handlers)
[GIN-debug] HEAD   /uploads/*filepath        --> github.com/gin-gonic/gin.(*RouterGroup).createStaticHandler.func1 (4 handlers)
[GIN-debug] GET    /api/releases             --> release-management-backend/controllers.(*ReleaseController).GetReleases-fm (4 handlers)
[GIN-debug] POST   /api/releases             --> release-management-backend/controllers.(*ReleaseController).CreateRelease-fm (4 handlers)
[GIN-debug] GET    /api/releases/:id         --> release-management-backend/controllers.(*ReleaseController).GetRelease-fm (4 handlers)
[GIN-debug] PUT    /api/releases/:id         --> release-management-backend/controllers.(*ReleaseController).UpdateRelease-fm (4 handlers)
[GIN-debug] DELETE /api/releases/:id         --> release-management-backend/controllers.(*ReleaseController).DeleteRelease-fm (4 handlers)
[GIN-debug] GET    /api/releases/:id/features --> release-management-backend/controllers.(*FeatureController).GetFeatures-fm (4 handlers)
[GIN-debug] POST   /api/releases/:id/features --> release-management-backend/controllers.(*FeatureController).CreateFeature-fm (4 handlers)
[GIN-debug] GET    /api/releases/:id/ccbs    --> release-management-backend/controllers.(*CCBController).GetCCBs-fm (4 handlers)
[GIN-debug] POST   /api/releases/:id/ccbs    --> release-management-backend/controllers.(*CCBController).CreateCCB-fm (4 handlers)
[GIN-debug] GET    /api/releases/:id/risks   --> release-management-backend/controllers.(*RiskController).GetRisks-fm (4 handlers)
[GIN-debug] POST   /api/releases/:id/risks   --> release-management-backend/controllers.(*RiskController).CreateRisk-fm (4 handlers)
[GIN-debug] GET    /api/releases/:id/qualities --> release-management-backend/controllers.(*QualityController).GetQualities-fm (4 handlers)
[GIN-debug] POST   /api/releases/:id/qualities --> release-management-backend/controllers.(*QualityController).CreateQuality-fm (4 handlers)
[GIN-debug] GET    /api/features/states      --> release-management-backend/controllers.(*FeatureController).GetFeatureStates-fm (4 handlers)
[GIN-debug] PUT    /api/features/:id         --> release-management-backend/controllers.(*FeatureController).UpdateFeature-fm (4 handlers)
[GIN-debug] DELETE /api/features/:id         --> release-management-backend/controllers.(*FeatureController).DeleteFeature-fm (4 handlers)
[GIN-debug] PUT    /api/ccbs/:id             --> release-management-backend/controllers.(*CCBController).UpdateCCB-fm (4 handlers)
[GIN-debug] DELETE /api/ccbs/:id             --> release-management-backend/controllers.(*CCBController).DeleteCCB-fm (4 handlers)
[GIN-debug] PUT    /api/risks/:id            --> release-management-backend/controllers.(*RiskController).UpdateRisk-fm (4 handlers)
[GIN-debug] DELETE /api/risks/:id            --> release-management-backend/controllers.(*RiskController).DeleteRisk-fm (4 handlers)
[GIN-debug] PUT    /api/qualities/:id        --> release-management-backend/controllers.(*QualityController).UpdateQuality-fm (4 handlers)
[GIN-debug] DELETE /api/qualities/:id        --> release-management-backend/controllers.(*QualityController).DeleteQuality-fm (4 handlers)
2025/06/20 15:48:55 Server starting on 0.0.0.0:8888
2025/06/20 15:48:55 API will be available at:
2025/06/20 15:48:55   - http://localhost:8888/api
2025/06/20 15:48:55   - http://***************:8888/api
2025/06/20 15:48:55   - http://127.0.0.1:8888/api
[GIN-debug] [WARNING] You trusted all proxies, this is NOT safe. We recommend you to set a value.
Please check https://pkg.go.dev/github.com/gin-gonic/gin#readme-don-t-trust-all-proxies for details.
[GIN-debug] Listening and serving HTTP on 0.0.0.0:8888
[GIN] 2025/06/20 - 15:49:03 | 200 |    6.870498ms | *************** | GET      "/api/releases"
[GIN] 2025/06/20 - 15:49:04 | 200 |    4.628193ms | *************** | GET      "/api/releases/1"
[GIN] 2025/06/20 - 15:49:04 | 200 |    6.994412ms | *************** | GET      "/uploads/quality/quality_1_1749803562.jpg"
[GIN] 2025/06/20 - 15:49:04 | 200 |     7.02439ms | *************** | GET      "/uploads/quality/quality_1_1749803566.jpg"
